'use client';

import React from 'react';
import { Appearance, loadStripe } from '@stripe/stripe-js';
import { CheckoutProvider } from '@stripe/react-stripe-js';
import {
  EmbeddedCheckoutProvider,
  EmbeddedCheckout
} from '@stripe/react-stripe-js';
import { getClientConfig } from '@/client-config/client-config';
import { StripeMode } from './types';
import { LoadingSpinner } from '@/icons/icons';

interface StripeProviderProps extends React.PropsWithChildren {
  clientSecret: string | undefined;
  mode: StripeMode;
  options?: {
    appearance?: Appearance;
    returnUrl?: string;
  };
}

const stripePromise = loadStripe(getClientConfig().stripePublicKey!, {
  betas: ['custom_checkout_beta_5']
});

export function StripeProvider({
  children,
  clientSecret,
  mode,
  options
}: StripeProviderProps) {
  if (!clientSecret) {
    return <LoadingSpinner />;
  }

  if (mode === 'embedded') {
    return (
      <EmbeddedCheckoutProvider
        stripe={stripePromise}
        options={{
          clientSecret,
          ...options
        }}
      >
        {children || <EmbeddedCheckout />}
      </EmbeddedCheckoutProvider>
    );
  }

  return (
    <CheckoutProvider
      stripe={stripePromise}
      options={{
        clientSecret,
        elementsOptions: { ...options }
      }}
    >
      {children}
    </CheckoutProvider>
  );
}
