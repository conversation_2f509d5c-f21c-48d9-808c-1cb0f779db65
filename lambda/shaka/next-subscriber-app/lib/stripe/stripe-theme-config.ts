import { StripeCheckoutElementsOptions } from '@stripe/stripe-js';

export const STRIPE_ELEMENTS_OPTIONS: StripeCheckoutElementsOptions = {
  appearance: {
    theme: 'flat',
    rules: {
      '.AccordionItem': {
        backgroundColor: 'transparent',
        borderColor: 'transparent',
        paddingLeft: '1px',
        paddingRight: '1px'
      },
      '.Label': {
        color: '#141414',
        fontWeight: '700',
        marginBottom: '8px'
      },
      '.Input': {
        marginBottom: '8px',
        borderRadius: '2px',
        color: '#141414',
        backgroundColor: '#fff',
        border: '1px solid #dddde0'
      },
      '.Input:focus': {
        boxShadow: 'none',
        outline: '1px solid  #141414'
      },
      '.Input--invalid': {
        boxShadow: 'none',
        color: '#ea0040'
      },
      '.Error': {
        color: '#ea0040',
        fontSize: '14px'
      }
    },
    variables: {
      colorPrimary: '#141414',
      iconCardErrorColor: '#141414',
      iconCardCvcErrorColor: '#141414'
    }
  }
};

export const PAYMENT_CONSTANTS = {
  PAYMENT_MODE: 'custom' as const,
  CARD_PREFIX: 'Card ending',
  POWERED_BY_TEXT: 'Powered by',
  EDIT_BUTTON_TEXT: 'Edit Payment',
  SUBMIT_BUTTON_TEXT: 'Change card details',
  PROCESSING_TEXT: 'Processing...',
  SETUP_LOADING_TEXT: 'Setting up payment form...',
  SUCCESS_TITLE: 'Card details updated',
  SUCCESS_MESSAGE: 'Your card details have been changed successfully.',
  ERROR_TITLE: 'Card details update failed',
  ERROR_MESSAGE: 'There was a problem updating your card. Please try again.',
  PAYMENT_ERROR_TITLE: 'Payment error',
  SETUP_ERROR_TITLE: 'Setup Error'
};
