// Basic type definitions for Stripe integration
export type StripeMode = 'elements' | 'embedded' | 'custom';

export type CreateSessionParams = {
  mode: StripeMode;
  // strongly typed using steps urls ?
  returnUrl: string;
};

export type SessionResponse = {
  clientSecret: string;
  sessionId?: string;
};

export type PaymentStatus =
  | 'succeeded'
  | 'processing'
  | 'requires_payment_method'
  | 'canceled';

export type UseStripeReturnHandlerOptions = {
  onSuccessRedirectUrl: string;
  onErrorRedirectUrl: string;
};
