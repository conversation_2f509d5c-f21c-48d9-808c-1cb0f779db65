export const API_ENDPOINTS = {
  dashboard: '/dashboard',
  auth: {
    login: '/auth/login/',
    logout: '/auth/logout/',
    refresh: '/auth/refresh-token/',
    otpResend: 'auth/verify/resend/',
    signup: 'auth/sign-up/',
    forgotPassword: 'auth/forgot-password/',
    resetPassword: '/auth/reset-password/'
  },
  otp: 'auth/verify/',
  deliveroo: {
    validateRider: 'deliveroo/validate-rider/'
  },
  stripe: {
    createCheckoutSession: (paymentMode: string, returnUrl: string) =>
      `basket/checkout/?ui_mode=${paymentMode}&return_path=${returnUrl}`,
    createSetupSession: (returnUrl: string) =>
      `subscription/change-card/?return_path=${returnUrl}`,
    checkoutSessionStatus: (sessionId: string) =>
      `checkout/session/status/?session_id=${sessionId}`,
    purchaseIntentDetails: (sessionId: string) =>
      `purchase-intent/details?session_id=${sessionId}`
  },
  wizard: {
    pacCode: (id: string) => `/data/port-in-requests/${id}/`,
    iphoneInstallationCode: () => '/iphone-installation-code/',
    universalLink: (token: string) => `secure-esim-data?token=${token}`
  },
  subscriptions: {
    base: 'data/subscriptions',
    subscriber: 'data/subscriber',
    byId: (id: string) => `/subscriptions/${id}`,
    cancel: () => `/journey/migrate-subscription/`,
    pause: (id: string) => `/subscriptions/${id}/pause`,
    resume: (migrationId: string) =>
      `/journey/migrate-subscription/${migrationId}`,
    shareEsim: (currentPlanId: string) =>
      `/subscriptions/${currentPlanId}/share-esim/`,
    labelPlan: (subscriptionId: string) => `/data/????/${subscriptionId}/`,
    addExtraRoamingData: (currentPlanId: string) =>
      `/subscriptions/${currentPlanId}/roaming-data/`
  },
  plans: {
    base: '/plans',
    byId: (id: string) => `/plans/${id}`
  },
  sims: {
    base: 'data/sims/',
    purchaseDetails: '/data/purchase-details/'
  }
} as const;

export type ApiEndpoints = typeof API_ENDPOINTS;

export const API_HOST =
  process.env.NEXT_PUBLIC_API_BASE_URL ?? 'http://localhost:8000';

// .get(`/checkout/session/status/?session_id=${sessionId}`)

// api.post(`/plans/register/${planId}/`).then((res) => res.data);
