'use client';

import { PlanDetailsCarousel } from '@/components/plan-card-carousel/plan-card-carousel';
import { PlainCard } from '@/components/plain-card/plain-card';
import { Divider } from '@/components/divider/divider';
import { FullDetailsButton } from '@/components/full-details-button/full-details-button';
import Modal from '@/components/modal/modal';
import { CloseIcon } from '@/icons/icons';
import React from 'react';
import { ExtraRoaming } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/extra-roaming';
import { CancelPlan } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/cancel-plan';
import { KeepYourNumber } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/keep-your-number';
import { PlanUpgrade } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/plan-upgrade';
import { LabelPlan } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/label-plan';
import { ESIMInstructions } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/esim-instructions';
import { EsimShare } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/esim-share';
import { Alert } from '@/components/alert/alert';
import { useSearchParams } from 'next/navigation';
import { tabConfig } from '@/src/uswitch/app/dashboard/esim/layout';
import { YourPlansLoadingSkeleton } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/your-plans-skeleton';
import { AccountBillingSkeleton } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/account-billing-skeleton';
import { HelpSectionSkeleton } from '@/src/uswitch/app/dashboard/esim/@yourplans/_components/help-section-skeleton';
import { CurrentPlanProvider } from '@/context/current-plan-context';
import { useSubscription } from '@/hooks/useSubscription';
import { unlimitedPlanPerks } from '@/utils/constants';

const tabFallbacks = {
  'your-plans': <YourPlansLoadingSkeleton />,
  'account-billing': <AccountBillingSkeleton />,
  help: <HelpSectionSkeleton />
};

export default function YourPlans() {
  const searchParams = useSearchParams();
  const activeTab = searchParams.get('tab') || tabConfig[0].value;

  const { subscriptions, loadingSubscriptions, subscriptionsError } =
    useSubscription();

  if (subscriptionsError) {
    return (
      <Alert message="We could not load your subscription details. Please try again later." />
    );
  }

  if (loadingSubscriptions || !subscriptions) {
    return tabFallbacks[activeTab];
  }

  return (
    <CurrentPlanProvider>
      <section className="grid grid-cols-1 gap-y-8 md:gap-x-6 md:gap-y-0 lg:grid-cols-[minmax(0,1.3fr)_minmax(0,0.7fr)] lg:gap-x-8 xl:grid-cols-[minmax(0,1fr)_minmax(0,0.6fr)] xl:gap-x-2">
        <PlanDetailsCarousel
          subscriptions={subscriptions}
          classes={{
            header: 'bg-gradient-to-l from-[#e0f0ff] to-[#a3d4ff]'
          }}
        />
        <div className="mt-8 lg:mt-0">
          <h2 className="mb-2">Manage plans</h2>
          <PlainCard className="mb-6 p-4" as="article">
            {actionGroups.map((group) => (
              <ActionGroup key={group.title} group={group} />
            ))}
          </PlainCard>
          <PlanOverview />
        </div>
      </section>
    </CurrentPlanProvider>
  );
}

interface ModalConfig {
  buttonText: string;
  modalTitle: string;
  component: React.ComponentType<{ setIsOpen: (isOpen: boolean) => void }>;
  componentProps?: Record<string, object>;
  modalSize?: string;
  buttonClassName?: string;
  showTitle?: boolean;
}

interface ActionGroup {
  title: string;
  actions: ModalConfig[];
}

function ModalButton({
  buttonText,
  modalTitle,
  component: Component,
  componentProps = {},
  modalSize = 'lg:max-w-3xl',
  buttonClassName = ' justify-self-start',
  showTitle = true
}: ModalConfig) {
  return (
    <FullDetailsButton text={buttonText} className={buttonClassName}>
      {({ isOpen, setIsOpen }) =>
        isOpen && (
          <Modal onOpenChange={setIsOpen} open={isOpen}>
            <Modal.Overlay />
            <Modal.Content className={`w-full rounded-lg p-6 ${modalSize}`}>
              <div className="mb-4 flex justify-end">
                <Modal.Close>
                  <CloseIcon />
                </Modal.Close>
              </div>
              {showTitle && (
                <Modal.Title className="mb-4 text-xs font-semibold">
                  {modalTitle}
                </Modal.Title>
              )}
              <Modal.Description as="div">
                <Component {...componentProps} setIsOpen={setIsOpen} />
              </Modal.Description>
            </Modal.Content>
          </Modal>
        )
      }
    </FullDetailsButton>
  );
}

interface ActionGroupProps {
  group: ActionGroup;
}

function ActionGroup({ group }: ActionGroupProps) {
  return (
    <>
      <h3 className={group.title === 'Subscriptions' ? '' : 'mt-4'}>
        {group.title}
      </h3>
      <Divider className="mt-1" />
      <div className="mt-2 grid grid-cols-1 gap-y-4 lg:block lg:space-y-4 lg:space-x-4">
        {group.actions.map((action) => (
          <ModalButton
            key={action.buttonText}
            {...action}
            buttonClassName="uswitchLink"
          />
        ))}
      </div>
    </>
  );
}

function PlanOverview() {
  return (
    <>
      <h2 className="mt-8 mb-2">Plan overview</h2>
      <PlainCard as="article" className="mb-11 p-0">
        <header className="flex items-center justify-between bg-gradient-to-l from-[#e0f0ff] to-[#a3d4ff] p-4">
          <h3 className="text-sm">Unlimited</h3>
          <strong className="text-sm">£15</strong>
        </header>
        <div className="flex flex-wrap gap-4 px-4 py-6">
          {unlimitedPlanPerks.map((perk) => (
            <span key={perk} className="chip">
              {perk}
            </span>
          ))}
        </div>
      </PlainCard>
    </>
  );
}

const actionGroups: ActionGroup[] = [
  {
    title: 'Subscriptions',
    actions: [
      {
        buttonText: 'Get more EU roaming',
        modalTitle: 'Get more EU roaming',
        component: ExtraRoaming
      },
      {
        buttonText: 'Upgrade plan',
        modalTitle: 'Upgrade Plan name',
        component: PlanUpgrade,
        showTitle: false
      },
      {
        buttonText: 'Cancel plan',
        modalTitle: "We're sorry to see you go",
        component: CancelPlan
      }
    ]
  },
  {
    title: 'Plan',
    actions: [
      {
        buttonText: 'Label your plan',
        modalTitle: 'Label your plan',
        component: LabelPlan,
        modalSize: 'lg:max-w-xl'
      },
      {
        buttonText: 'Keep your number',
        modalTitle: 'Keep your existing number',
        component: KeepYourNumber
      }
    ]
  },
  {
    title: 'eSIM',
    actions: [
      {
        buttonText: 'Share eSIM',
        modalTitle: 'Share your eSIM',
        component: EsimShare,
        modalSize: 'lg:max-w-xl',
        buttonClassName: 'mt-2 w-fit'
      },
      {
        buttonText: 'Installation instructions',
        modalTitle: 'eSIM Installation instructions',
        component: ESIMInstructions,
        buttonClassName: 'mt-2 w-fit'
      }
    ]
  }
];
