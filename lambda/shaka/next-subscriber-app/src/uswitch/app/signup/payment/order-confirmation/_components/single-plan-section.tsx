import {
  checkIsAndroid,
  checkIsDesktop,
  checkIsIos,
  checkIsSafari
} from '@/utils/helpers';
import Button from '@/components/button/button';
import {
  InstructionStepsAndroid,
  InstructionStepsIos
} from '@/src/uswitch/app/signup/payment/order-confirmation/_components/instruction-steps-android';
import { Alert } from '@/components/alert/alert';
import { QrCodeSkeleton } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/qr-code-skeleton';
import { QRCode } from '@/src/uswitch/app/signup/payment/_components/qrcode';
import { ConditionalWrapper } from '@/components/conditional-wrapper/conditional-wrapper';
import { InfoGrid } from '@/src/uswitch/app/signup/payment/_components/info-grid';
import { Divider } from '@/components/divider/divider';
import { VideoInstructions } from '@/src/uswitch/app/signup/payment/_components/video-instructions';
import { AccountAccessSection } from '@/src/uswitch/app/signup/payment/_components/account-access';
import { HelpSection } from '@/src/uswitch/app/signup/payment/_components/help-section';
import { AfterPaymentOrderSummary } from '@/src/uswitch/app/signup/_components/order-summary/order-summary';
import { TermsModalButton } from '@/src/uswitch/app/signup/payment/order-confirmation/_components/terms-modal-button';
import React from 'react';
import { InstructionBaseWrapper } from './instruction-base-wrapper';
import { useOrderSummary } from '@/src/uswitch/app/signup/context/OrderSummaryContext';

interface SinglePlanSectionProps {
  // type ?????
  eSims: any;
  isPolling: boolean;
  instructionError: Error | null;
  totalCost: string;
}

export function SinglePlanSection({
  eSims,
  instructionError,
  isPolling,
  totalCost
}: SinglePlanSectionProps) {
  const { orderSummary } = useOrderSummary();
  const { esim_data } = eSims?.[0] || {};

  // in case of iphone we need ios_universal_link
  // andorid - android_activation_data

  console.log(orderSummary.basketSummary, 'basketSummary');

  if (checkIsIos() && checkIsSafari()) {
    // separate component with state to manage ios universal link ?? - check subs app
    return (
      <div className="px-6 pt-4">
        <InstructionBaseWrapper type="ios-safari">
          <Button
            onClick={() => console.log(esim_data.ios_universal_link)}
            className="w-full"
            variant="primary"
          >
            Install eSIM
          </Button>
        </InstructionBaseWrapper>
      </div>
    );
  } else if (checkIsIos()) {
    return (
      <div className="px-6 pt-4">
        <InstructionBaseWrapper type="ios">
          <InstructionStepsIos />
          {instructionError && (
            <Alert
              className="w-fit p-6"
              message={'We could not generate qr code.'}
            />
          )}
          {isPolling ? (
            <QrCodeSkeleton size="max" />
          ) : (
            <QRCode
              size="max"
              qrcode={esim_data?.qr_code_image || ''}
              className="mt-4"
            />
          )}
        </InstructionBaseWrapper>
      </div>
    );
  } else if (checkIsAndroid()) {
    // andoird activation_data here ? - check subs app
    return (
      <div className="px-6 pt-4">
        <InstructionBaseWrapper type="android">
          <InstructionStepsAndroid />
        </InstructionBaseWrapper>
      </div>
    );
  } else if (checkIsDesktop()) {
    return (
      <div className="mx-auto max-w-[827px]">
        <div className="flex flex-col gap-2">
          <ConditionalWrapper className="px-6 pt-6 lg:p-6">
            <InstructionBaseWrapper type="desktop">
              <div className="flex flex-wrap items-center gap-4 lg:gap-8">
                {instructionError && (
                  <Alert message={'We could not generate qr code.'} />
                )}
                {isPolling ? (
                  <QrCodeSkeleton size="large" />
                ) : (
                  <QRCode
                    qrcode={esim_data?.qr_code_image || ''}
                    size="large"
                  />
                )}
                <InfoGrid />
              </div>
              <Divider className="my-6" />
              <VideoInstructions />
            </InstructionBaseWrapper>
          </ConditionalWrapper>
          <Divider className="mx-auto block w-[calc(100%-48px)] lg:hidden" />
          <ConditionalWrapper className="px-6 lg:my-4 lg:p-6">
            <AccountAccessSection />
          </ConditionalWrapper>
          <Divider className="mx-auto block w-[calc(100%-48px)] lg:hidden" />
          <ConditionalWrapper className="px-6 lg:my-4 lg:p-6">
            <h2 className="mb-6">Need some help?</h2>
            <HelpSection />
          </ConditionalWrapper>
          <Divider className="mx-auto block w-[calc(100%-48px)] lg:hidden" />
          <ConditionalWrapper className="px-6 lg:p-6">
            <AfterPaymentOrderSummary
              basketSummary={orderSummary.basketSummary}
              cardLastThreeDigits={123}
              totalCost={totalCost}
            />
            <Divider />
            <div className="my-4">
              <p className="text-xxxs inline opacity-100">Full </p>
              <TermsModalButton />
              <p className="text-xxxs inline opacity-100"> apply.</p>
            </div>
          </ConditionalWrapper>
        </div>
      </div>
    );
  }
}
