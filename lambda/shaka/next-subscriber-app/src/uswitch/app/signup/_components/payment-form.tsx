'use client';

import React from 'react';
import {
  PaymentElement,
  ExpressCheckoutElement
} from '@stripe/react-stripe-js';
import Button from '@/components/button/button';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { usePaymentFormSubmission } from '@/hooks/usePaymentFormSubmission';

const expressCheckoutOptions = {
  paymentMethodOrder: ['apple_pay', 'google_pay', 'card', 'link'],
  buttonType: {
    applePay: 'plain' as const,
    googlePay: 'plain' as const
  },
  buttonHeight: 48,
  buttonTheme: {
    applePay: 'white' as const,
    googlePay: 'white' as const
  },
  layout: {
    maxColumns: 1,
    maxRows: 1
  },
  paymentMethods: {
    applePay: 'always' as const,
    googlePay: 'always' as const
  }
};

type PaymentFormProps = {
  includeExpress?: boolean;
};

export function PaymentForm({ includeExpress = true }: PaymentFormProps) {
  const {
    handleSubmit,
    handleExpressCheckout,
    handleExpressCancel,
    handleExpressLoadError,
    isPending,
    errors
  } = usePaymentFormSubmission();

  return (
    <div className="space-y-6">
      {includeExpress && (
        <div className="express-checkout-section">
          <ExpressCheckoutElement
            options={expressCheckoutOptions}
            onConfirm={handleExpressCheckout}
            onCancel={handleExpressCancel}
            onLoadError={handleExpressLoadError}
          />
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <PaymentElement />
        <Button
          isLoading={isPending}
          className="bg-primary hover:bg-primary-hover text-bold mt-2 inline-block w-full cursor-pointer rounded-[2px] p-3 text-center font-semibold text-white"
        >
          {isPending ? 'Processing payment...' : 'Pay'}
        </Button>
        {errors.length > 0 && (
          <FormAlert
            className="mt-4"
            title="Payment failed"
            variant="error"
            titleStyles="text-text!"
            singleMessageStyles="text-text!"
            messages={[`error: ${errors[0]}`]}
          />
        )}
      </form>
    </div>
  );
}
