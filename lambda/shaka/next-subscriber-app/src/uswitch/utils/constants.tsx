import { CameraIcon, SimIcon } from '@/icons/icons';
import { OrderItem } from '@/utils/helpers';

export type SubscriptionStatusKey = keyof typeof subscriptionStatus;
export type SubscriptionStatusInfo =
  (typeof subscriptionStatus)[SubscriptionStatusKey];

export const subscriptionStatus = {
  inactive: {
    name: 'inactive',
    color: 'red-500'
  },
  activating: {
    name: 'activating',
    color: 'purple-500'
  },
  active: {
    name: 'active',
    color: 'success-border'
  },
  cancelled: {
    name: 'cancelled',
    color: 'red-500'
  }
} as const;

export interface PlanState {
  mainPlanQuantity: number;
  familyPlanQuantity: number;
}

export const planOrderItems: OrderItem[] = [
  {
    id: 1,
    name: 'Unlimited SIM plan',
    price: 15,
    description: {
      unlimitedTexts: 'Unlimited UK texts',
      unlimitedMinutes: 'Unlimited UK minutes',
      unlimitedData: 'Unlimited UK data (we might limit you after 650GB)',
      euRoaming: 'European roaming up to 20GB per month.',
      euRoamingLimit: 'Limited to a 10 days per trip.',
      fiveG: '5G network speeds',
      cancelAnytime: 'Cancel whenever you want',
      keepNumber: 'Switch and keep your number in one simple step'
    }
  },
  {
    id: 2,
    name: '40GB SIM plan',
    price: 8,
    description: {
      unlimitedTexts: 'Unlimited UK texts',
      unlimitedMinutes: 'Unlimited UK minutes',
      unlimitedData: 'Unlimited UK data (we might limit you after 650GB)',
      euRoaming: 'European roaming up to 20GB per month.',
      euRoamingLimit: 'Limited to a 10 days per trip.',
      fiveG: '5G network speeds',
      cancelAnytime: 'Cancel whenever you want',
      keepNumber: 'Switch and keep your number in one simple step'
    }
  }
];

export const helpSectionCardStatus = {
  available: {
    color: 'bg-success-border',
    text: 'Available'
  }
};

export const infoGrid = [
  // {
  //   icon: WifiIcon,
  //   text: 'Make sure you are connected to the internet'
  // },
  {
    icon: CameraIcon,
    text: 'Scan the QR code with your phone'
  },
  {
    icon: SimIcon,
    text: 'Click “Add eSIM” and finish the set-up process'
  }
];
