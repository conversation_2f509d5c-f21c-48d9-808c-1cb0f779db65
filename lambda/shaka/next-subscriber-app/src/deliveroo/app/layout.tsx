import React, { Suspense } from 'react';
import './globals.css';
import { Metadata } from 'next';
import { headers } from 'next/headers';
import { getClientConfig } from '@/client-config/client-config';
import { MSWProvider, ReactQueryProvider } from '@/providers/providers';
import { PHProvider } from '@/lib/analytics/posthog-provider';
import { AuthProvider } from '@/auth/context/auth-context';
import { DeliveryRiderAuthProvider } from '@/src/deliveroo/app/signup/context/DeliveryRiderAuth';

export async function generateMetadata() {
  const config = getClientConfig();
  const headersList = headers();
  const list = await headersList;
  const host = list.get('host');
  const protocol = list.get('x-forwarded-proto') || 'https';
  const siteUrl = `${protocol}://${host}`;

  const metadata: Metadata = {
    title: config.name,
    description: '',
    keywords: [],
    robots: 'noindex, nofollow',
    openGraph: {
      title: config.name,
      description: '',
      url: siteUrl,
      siteName: '',
      images: [
        {
          url: `${siteUrl}/images/og-image.jpg`,
          width: 800,
          height: 600
        }
      ],
      type: 'website'
    }
  };

  return metadata;
}

// TODO: add google analytics and gtag

export default function RootLayout({
  children
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className="antialiased">
        <MSWProvider>
          <ReactQueryProvider>
            <Suspense fallback={null}>
              <PHProvider>
                <DeliveryRiderAuthProvider>
                  <AuthProvider>{children}</AuthProvider>
                </DeliveryRiderAuthProvider>
              </PHProvider>
            </Suspense>
          </ReactQueryProvider>
        </MSWProvider>
      </body>
    </html>
  );
}
