'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import { ProgressBar } from '@/components/progress-bar/progress-bar';
import { DesktopOnly } from '@/components/conditional-wrapper/conditional-wrapper';
import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';

interface SignupProgressBarProps {
  ariaLabel?: string;
  withLabel?: boolean;
  barClassName?: string;
  trackClassName?: string;
  desktopOnly?: boolean;
}

// move
export function shouldShowProgressBar(pathname: string): boolean {
  return (
    pathname !== ROUTES_CONFIG['order-confirmation'].path &&
    pathname !== ROUTES_CONFIG['payment-error'].path &&
    pathname !== ROUTES_CONFIG['sign-in'].path
  );
}

export function SignupProgressBar({
  ariaLabel = 'register progress bar',
  withLabel = true,
  barClassName = 'bg-custom-gradient mb-6',
  trackClassName = 'bg-[linear-gradient(180deg, #1414141a 0%, #1414140d 100%)]',
  desktopOnly = true
}: SignupProgressBarProps) {
  const pathname = usePathname();
  shouldShowProgressBar(pathname);

  if (!shouldShowProgressBar(pathname)) {
    return null;
  }

  const progressBarContent = (
    <ProgressBar
      withLabel={withLabel}
      ariaLabel={ariaLabel}
      textColor="text-[#717171]"
    >
      <ProgressBar.Track className={trackClassName}>
        <ProgressBar.Bar className={barClassName} />
      </ProgressBar.Track>
    </ProgressBar>
  );

  if (desktopOnly) {
    return <DesktopOnly>{progressBarContent}</DesktopOnly>;
  }

  return progressBarContent;
}
