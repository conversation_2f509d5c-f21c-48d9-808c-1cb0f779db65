'use client';

import Image from 'next/image';
import { <PERSON><PERSON><PERSON><PERSON> } from '@/context/wizard/wizard';
import React, { ReactNode } from 'react';
import { ProgressBar } from '@/components/progress-bar/progress-bar';
import { NavBar } from '@/components/nav-bar/nav-bar';
import logo from '../../public/images/deliveroo-logo.png';
import { usePathname } from 'next/navigation';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { Step, STEP_ROUTES, STEPS } from '@/src/deliveroo/app/steps';
import { PlanSelectionProvider } from '@/src/deliveroo/app/signup/context/SignupContext';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import { SignupStepRestorer } from '@/components/signup-step-restorer/signup-step-restorer';
// import { SignupProgressProvider } from '@/context/signup-progress-context/signup-progress-tracked-routes';

function shouldShowProgressBar(pathname: string): boolean {
  return (
    pathname !== ROUTES_CONFIG['order-confirmation'].path &&
    pathname !== ROUTES_CONFIG['payment-error'].path &&
    pathname !== ROUTES_CONFIG['payment-return'].path &&
    pathname !== ROUTES_CONFIG['sign-in'].path &&
    pathname !== ROUTES_CONFIG['iphone-installation'].path
  );
}

// TODO: check which routes can be dynamically loaded

// IMPORTANT !!! FOR NOW reuireAuth comppnent wraps all signup pages, the question is what happens after shaka registration process ! Do I need to
// separate this two processes ? Create a new component to check for deliveroo user?

export default function SignupLayout({ children }: { children: ReactNode }) {
  const isMobileDevice = useMediaQuery(1024);
  const pathname = usePathname();
  const showProgressBar = shouldShowProgressBar(pathname);

  // MAY NOT BE NEEDED! Check once app is ready ( with deliveroo id auth )
  // useEffect(() => {
  //   localStorage.setItem(
  //     LocalKey.SIGNUP_PROGRESS,
  //     JSON.stringify({
  //       isRegistered: false,
  //       isPaymentCompleted: false,
  //       isOtpVerified: false
  //     })
  //   );
  // }, []);

  return (
    <WizardProvider<Step> steps={STEPS} stepRoutes={STEP_ROUTES}>
      <PlanSelectionProvider>
        <NavBar
          logo={
            <Image
              src={logo}
              priority
              alt="Deliveroo logo"
              width={236}
              height={30}
            />
          }
          className="min-h-[56px]"
        />
        {isMobileDevice ? (
          <main className="layout-height block bg-white lg:hidden">
            <SignupStepRestorer />
            {showProgressBar && (
              <div className="px-[var(--spacing-6)] pt-[var(--spacing-6)]">
                <ProgressBar
                  textColor="text-[#717171]"
                  withLabel
                  ariaLabel="sign-up progress bar"
                >
                  <ProgressBar.Track className="bg-[linear-gradient(180deg, #1414141a 0%, #1414140d 100%)]">
                    <ProgressBar.Bar className="bg-custom-gradient mb-6" />
                  </ProgressBar.Track>
                </ProgressBar>
              </div>
            )}
            {children}
          </main>
        ) : (
          <main className="layout-height bg-tertiary mx-auto hidden px-4 py-[var(--spacing-6)] lg:block lg:py-[var(--spacing-7)] xl:px-0 2xl:py-[var(--spacing-8)]">
            <SignupStepRestorer />
            {children}
          </main>
        )}
      </PlanSelectionProvider>
    </WizardProvider>
  );
}
