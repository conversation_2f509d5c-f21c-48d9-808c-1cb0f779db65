'use client';

import React, { useMemo } from 'react';
import Wrapper from '@/components/wrapper/wrapper';
import { ConditionalWrapper } from '@/components/conditional-wrapper/conditional-wrapper';
import { Divider } from '@/components/divider/divider';

import { CodeInfoGrid } from '@/src/deliveroo/app/signup/number-porting/_components/code-info-grid';
import { SinglePacForm } from '@/src/deliveroo/app/signup/number-porting/_components/single-pac-form';
import { MultiPacForm } from '@/src/deliveroo/app/signup/number-porting/_components/multi-pac-form';
import { SignupProgressBar } from '@/src/deliveroo/app/signup/_components/signup-progress-bar/signup-progress-bar';
import { useOrderSummary } from '../context/OrderSummaryContext';
import { OrderSummary } from '@/src/deliveroo/app/signup/_components/order-summary/order-summary';
import { generatePlanInstanceList } from '@/src/deliveroo/utils/helpers';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import { Loader } from '@/components/loader/loader';
import { RequireAuth } from '@/components/require-auth/require-auth';

export default function NumberPorting() {
  const { orderSummary } = useOrderSummary();

  // multi sims !!! - need a new function without addons
  const basketItemList = useMemo(
    () => generatePlanInstanceList(orderSummary.basketSummary),
    [orderSummary.basketSummary]
  );

  const isMultipleEsimOrder = basketItemList.length > 1;

  return (
    <RequireAuth<typeof ROUTES_CONFIG>
      loadingComponent={<Loader />}
      redirectTo={ROUTES_CONFIG.login.path}
    >
      <Wrapper>
        <div className="left-column">
          <ConditionalWrapper className="left-column-inner mb-0 rounded border-gray-100 shadow-none lg:border">
            <SignupProgressBar />
            <NumberPortingHeader />
            <Divider className="mt-6 hidden border lg:block" />
            {/*<NumberPortingTabs />*/}
            <h2 >Keep my number</h2>
            <CodeInfoGrid />
            <Divider className="mt-8 mb-6" />
            {isMultipleEsimOrder ? (
              <MultiPacForm basketItemList={basketItemList} />
            ) : (
              <SinglePacForm />
            )}
          </ConditionalWrapper>
        </div>
        <OrderSummary
          basketSummary={orderSummary.basketSummary}
          totalCost={orderSummary.totalCost}
        />
      </Wrapper>
    </RequireAuth>
  );
}

function NumberPortingHeader() {
  return (
    <>
      <h1 className="mb-4 text-base font-normal lg:mb-0">
        Switching from another network
      </h1>
      <p >
        We&apos;ll handle the switch from your current provider and can move
        your existing number over for you.
      </p>
    </>
  );
}

// export function NumberPortingTabs() {
//   const [activeTab, setActiveTab] = useState<Tab>(tabConfig[0].value);
//
//   return (
//     <>
//       <div
//         className="relative mt-4 flex w-full border-b border-gray-300 lg:mt-0 lg:w-full"
//         role="tablist"
//       >
//         {tabConfig.map(({ value, label }, index) => (
//           <button
//             key={value}
//             onClick={() => setActiveTab(value)}
//             className={getTabButtonClass(value, activeTab)}
//             role="tab"
//             aria-selected={value === activeTab}
//             aria-controls={`tabpanel-${value}`}
//             id={`tab-${value}`}
//             tabIndex={value === activeTab ? 0 : -1}
//           >
//             {label}
//           </button>
//         ))}
//       </div>
//       <div
//         role="tabpanel"
//         id={`tabpanel-${activeTab}`}
//         aria-labelledby={`tab-${activeTab}`}
//       >
//         {activeTab === 'keep-my-number' ? (
//           <CodeInfoGrid />
//         ) : (
//           <StackCodeInfoGrid />
//         )}
//       </div>
//     </>
//   );
// }
