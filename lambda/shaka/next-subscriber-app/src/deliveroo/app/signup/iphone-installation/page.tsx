'use client';

import { useMediaQuery } from '@/hooks/useMediaQuery';
import { useSearchParams } from 'next/navigation';
import { Divider } from '@/components/divider/divider';
import React from 'react';
import Button from '@/components/button/button';
import { useGetUniversalLink } from '@/hooks/useGetUniversalLink';
import { checkIsIos, checkIsSafari } from '@/utils/helpers';
import { InstructionStepsIos } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/instruction-steps-iphone';
import Image from 'next/image';
import appleIcon from '@/public/images/apple-icon.png';
import { useShowEsimInstructions } from '@/hooks/useShowEsimInstructions';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { IphoneSafariInfoAlert } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/iphone-safari-info-alert';
import { GenericModal } from '@/components/generic-modal/generic-modal';
import { Loader } from '@/components/loader/loader';
import { NextStepLink } from '@/components/cta-button/next-step-link';

export default function IPhoneESimInstallationPage() {
  const isMobileDevice = useMediaQuery(1024);

  if (!isMobileDevice) {
    return (
      <FormAlert
        variant="info"
        messages={['message: This page is accessible only on mobile devices']}
        dismissible={false}
      />
    );
  }

  const isSafariBrowser = checkIsIos() && checkIsSafari();

  if (!isSafariBrowser) {
    return <BrowserIncompatibleView />;
  }

  return <ESimInstallationView />;
}

function ESimInstallationView() {
  const searchParams = useSearchParams();
  const token = searchParams.get('token') || '';

  const { universalLink, isPendingUniversalLink, universalLinkError } =
    useGetUniversalLink(token);

  if (isPendingUniversalLink) {
    return <Loader />;
  }

  return (
    <div className="px-[var(--spacing-6)] pt-[var(--spacing-6)]">
      <h2 className="text-sm">iPhone eSIM setup</h2>
      <Divider className="mb-4" />
      <h2 >Install your eSIM</h2>
      <p className="mb-5">
        Follow the system prompt to install your eSIM.
      </p>
      {universalLinkError ? (
        <ESimInstallationError
          errorMessage={universalLinkError?.message}
          fallbackMessage="We could not fetch the installation code"
        />
      ) : (
        <NextStepLink
          openInNewWindow
          className="bg-black!"
          variant="primary"
          text="Install eSIM"
          href={universalLink}
        />
      )}
      <Divider className="my-4" />
      <ESimInstructionsSection />
    </div>
  );
}

function ESimInstructionsSection() {
  const { isModalOpen, handleAppleClick, handleModalClose } =
    useShowEsimInstructions();

  return (
    <>
      <h3 className="mb-2">Video instructions</h3>
      <p >
        Watch a brief instructional video to help you get your new eSIM
        installed
      </p>
      <Button
        onClick={handleAppleClick}
        className="mt-4 w-full sm:w-fit"
        variant="secondary"
      >
        <Image width={23} height={23} src={appleIcon} alt="iPhone icon" />
        <span>iPhone instructions</span>
      </Button>
      {isModalOpen && (
        <GenericModal
          setIsOpen={handleModalClose}
          isOpen={isModalOpen}
          modalTitle="adadasd"
          modalDescription="asdasdas"
        />
      )}
      <Divider className="my-4" />
      <InstructionStepsIos />
      <Divider className="my-4" />
      <h2 >Finish your setup</h2>
      <p className="mb-5">
        Go back where you left off to finish setup and to access your account.
      </p>
    </>
  );
}

function BrowserIncompatibleView() {
  return (
    <div className="px-[var(--spacing-6)] pt-[var(--spacing-6)]">
      <h2 >Install your eSIM</h2>
      <p className="mb-5">
        Make sure this page is opened in the{' '}
        <span className="font-bold">Safari browser</span>.
      </p>
      <IphoneSafariInfoAlert className="border-[#E762C4] bg-[#FFE0F7]">
        Your browser <span className="font-bold">isn’t compatible</span>. open
        this page in the <span className="font-bold">Safari browser</span>{' '}
        instead.
      </IphoneSafariInfoAlert>
      <Divider className="my-4" />
      <ESimInstructionsSection />
    </div>
  );
}

interface ErrorMessageProps {
  errorMessage: string | undefined;
  fallbackMessage?: string;
}

function ESimInstallationError({
  errorMessage,
  fallbackMessage
}: ErrorMessageProps) {
  return (
    <FormAlert
      className="mt-4"
      singleMessageStyles=""
      titleStyles=""
      title="Error"
      variant="error"
      messages={[`error: ${errorMessage || fallbackMessage}`]}
    />
  );
}
