'use client';

import React from 'react';
import OTPInput from '@/components/otp/otp';
import { Divider } from '@/components/divider/divider';
import { ConditionalWrapper } from '@/components/conditional-wrapper/conditional-wrapper';
import { useOtpVerification } from '@/hooks/useOtpVerification';
import { SignupProgressBar } from '@/src/deliveroo/app/signup/_components/signup-progress-bar/signup-progress-bar';
import Wrapper from '@/components/wrapper/wrapper';
import { usePlanSelection } from '@/src/deliveroo/app/signup/context/SignupContext';
import { Alert } from '@/components/alert/alert';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { OrderSummary } from '@/src/deliveroo/app/signup/_components/order-summary/order-summary';
import Button from '@/components/button/button';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import { Loader } from '@/components/loader/loader';
import { RequireAuth } from '@/components/require-auth/require-auth';
import { MAX_MAIN_PLAN_QUANTITY } from '@/src/deliveroo/app/signup/_reducer/reducer';
// import { useSignupProgress } from '@/context/signup-progress-context/signup-progress-tracked-routes';
// import { RequireStepAuth } from '@/components/signup-progress-check/signup-progress-check';

export default function OTPVerificationPage() {
  // const { updateProgress } = useSignupProgress();
  const { orderSummary } = usePlanSelection(MAX_MAIN_PLAN_QUANTITY);
  const {
    setOtp,
    errors,
    setErrors,
    resetOtp,
    handleSubmit,
    isPending,
    isResendRequestPending,
    otpResendError,
    isOtpCodeResend,
    setOtpCodeResend
  } = useOtpVerification({ length: 4 });

  const isBusy = isPending || isResendRequestPending;
  const hasErrors = otpResendError || errors[0];

  // const { saveCurrentStep } = useCurrentSignupStep({
  //   requiresAuth: true,
  //   brand: 'deliveroo'
  // });

  return (
    <RequireAuth<typeof ROUTES_CONFIG>
      loadingComponent={<Loader />}
      redirectTo={ROUTES_CONFIG['plan-selection'].path}
    >
      <Wrapper>
        <div className="left-column">
          <ConditionalWrapper className="rounded border-gray-100 p-6 pt-0 shadow-none lg:mb-0 lg:border lg:pt-6">
            <SignupProgressBar />
            <h1 className="mb-4">Email validation</h1>
            <p>
              We just sent you a 4 digit code to verify your email address.
              Please input it here
            </p>
            <form onSubmit={handleSubmit}>
              <div className="mt-8">
                <OTPInput
                  className={`lg:text-2xl} h-[75px] w-[50px] border border-black text-xl underline lg:h-[141px] lg:w-[100px] ${errors.length ? 'border-2 border-red-500' : ''}`}
                  onChange={setOtp}
                  length={4}
                  setInputError={setErrors}
                />
                {hasErrors && (
                  <Alert
                    variant="error"
                    message={otpResendError?.message ?? errors[0]}
                    className="mx-auto mt-4 w-fit"
                  />
                )}
              </div>
              <p className="text-default mt-8 text-center font-semibold">
                Code not received? Request a{' '}
                <button
                  type="button"
                  onClick={resetOtp}
                  className="cursor-pointer underline"
                >
                  new one
                </button>
                .
              </p>
              {isOtpCodeResend && (
                <FormAlert
                  autoHide
                  variant="deliveroo"
                  onDismiss={() => setOtpCodeResend(false)}
                  className="mt-4"
                  messages={[
                    'message: Code resent successfully! Please check your email'
                  ]}
                />
              )}
              <Divider className="mb-0" />
              <Button
                aria-disabled={isBusy}
                disabled={isBusy}
                className="mt-6 w-full"
                variant="primary"
                isLoading={isBusy}
              >
                Continue
              </Button>
            </form>
          </ConditionalWrapper>
        </div>
        <OrderSummary
          basketSummary={orderSummary.basketSummary}
          totalCost={orderSummary.totalCost}
        />
      </Wrapper>
    </RequireAuth>
  );
}
