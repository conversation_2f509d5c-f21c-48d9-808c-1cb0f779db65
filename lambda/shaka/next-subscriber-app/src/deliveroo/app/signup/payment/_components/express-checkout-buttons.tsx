'use client';

import React from 'react';
import { ExpressCheckoutElement } from '@stripe/react-stripe-js';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { usePaymentFormSubmission } from '@/hooks/usePaymentFormSubmission';

const expressCheckoutOptions = {
  paymentMethodOrder: ['apple_pay', 'google_pay', 'card', 'link'],
  buttonType: {
    applePay: 'plain' as const,
    googlePay: 'plain' as const
  },
  buttonHeight: 48,
  buttonTheme: {
    applePay: 'white' as const,
    googlePay: 'white' as const
  },
  layout: {
    maxColumns: 1,
    maxRows: 1
  },
  paymentMethods: {
    applePay: 'always' as const,
    googlePay: 'always' as const
  }
};

export function ExpressCheckoutButtons() {
  const {
    handleExpressCheckout,
    handleExpressCancel,
    handleExpressLoadError,
    errors
  } = usePaymentFormSubmission();

  return (
    <div className="express-checkout-container">
      <ExpressCheckoutElement
        options={expressCheckoutOptions}
        onConfirm={handleExpressCheckout}
        onCancel={handleExpressCancel}
        onLoadError={handleExpressLoadError}
      />
      {errors.length > 0 && (
        <FormAlert
          className="mt-4"
          title="Payment failed"
          variant="error"
          titleStyles=""
          singleMessageStyles=""
          messages={[`error: ${errors[0]}`]}
        />
      )}
    </div>
  );
}
