import {
  checkIs<PERSON>ndroid,
  checkIsDesktop,
  checkIsIos,
  checkIsSafari
} from '@/utils/helpers';
import { InstructionStepsAndroid } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/instruction-steps-android';
import { Alert } from '@/components/alert/alert';
import { QrCodeSkeleton } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/qr-code-skeleton';
import { QRCode } from '@/src/deliveroo/app/signup/payment/_components/qrcode';
import { ConditionalWrapper } from '@/components/conditional-wrapper/conditional-wrapper';
import { InfoGrid } from '@/src/deliveroo/app/signup/payment/_components/info-grid';
import { Divider } from '@/components/divider/divider';
import { VideoInstructions } from '@/src/deliveroo/app/signup/payment/_components/video-instructions';
import { AccountAccessSection } from '@/src/deliveroo/app/signup/payment/_components/account-access';
import { HelpSection } from '@/src/deliveroo/app/signup/payment/_components/help-section';
import { TermsModalButton } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/terms-modal-button';
import React from 'react';
import { InstructionBaseWrapper } from './instruction-base-wrapper';
import { AfterPaymentOrderSummary } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/after-payment-order-summary';
import {
  InstructionStepsIos,
  InstructionStepsIosNonSafari
} from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/instruction-steps-iphone';
import { InstructionStepDesktop } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/instruction-step-desktop';
import { NextStepLink } from '@/components/cta-button/next-step-link';
import { SimsApiResponse } from '@/schemas/schemas';
import { DataState } from '../types';
import { getButtonText } from '@/src/deliveroo/utils/helpers';
import { usePurchaseIntentDetails } from '@/hooks/usePurchaseIntentDetails';
import { useSearchParams } from 'next/navigation';
import { LoadingSpinner } from '@/icons/icons';

interface SinglePlanSectionProps {
  dataState: DataState;
  totalCost: string;
  esimData: SimsApiResponse[number]['esim_data'] | undefined;
}

export function SinglePlanSection({
  dataState,
  totalCost,
  esimData
}: SinglePlanSectionProps) {
  if (checkIsIos() && checkIsSafari()) {
    return (
      <IosSafariInstruction
        dataState={dataState}
        universalLink={esimData?.ios_universal_link}
      />
    );
  } else if (checkIsIos()) {
    return <NonIosSafariInstruction dataState={dataState} />;
  } else if (checkIsAndroid()) {
    return <AndroidInstruction dataState={dataState} />;
  } else if (checkIsDesktop()) {
    return (
      <DesktopInstruction
        dataState={dataState}
        esimData={esimData}
        totalCost={totalCost}
      />
    );
  }
}

interface IosSafariInstructionProps {
  dataState: DataState;
  universalLink: string | undefined;
}

function IosSafariInstruction({
  dataState,
  universalLink
}: IosSafariInstructionProps) {
  const shouldShowPendingState = dataState.isLoading;
  const isUniversalLinkUnavailable = !dataState.simsReady;
  return (
    <div className="px-6 pt-4">
      <InstructionBaseWrapper dataState={dataState} type="ios-safari">
        <NextStepLink
          disabled={shouldShowPendingState || isUniversalLinkUnavailable}
          isLoading={shouldShowPendingState}
          openInNewWindow
          className="w-full border-black! bg-black!"
          variant="primary"
          text={getButtonText(
            shouldShowPendingState,
            isUniversalLinkUnavailable
          )}
          href={dataState.simsReady ? universalLink : undefined}
        />
        <Divider className="my-6" />
        <InstructionStepsIos />
      </InstructionBaseWrapper>
    </div>
  );
}
function NonIosSafariInstruction({ dataState }: { dataState: DataState }) {
  const shouldShowPendingState = dataState.isLoading;
  return (
    <div className="px-6 pt-4">
      <InstructionStepsIosNonSafari
        isEsimReady={dataState.simsReady}
        shouldShowPendingState={shouldShowPendingState}
        hasPollingError={!!dataState.error}
      />
      <Divider className="my-6" />
      <InstructionStepsIos />
    </div>
  );
}
function AndroidInstruction({ dataState }: { dataState: DataState }) {
  return (
    <div className="px-6 pt-4">
      <InstructionBaseWrapper dataState={dataState} type="android">
        <InstructionStepsAndroid />
      </InstructionBaseWrapper>
    </div>
  );
}

interface DesktopInstructionProps {
  dataState: DataState;
  esimData: SimsApiResponse[number]['esim_data'] | undefined;
  totalCost: string;
}

function DesktopInstruction({ dataState, esimData }: DesktopInstructionProps) {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id') || '';
  const {
    purchaseIntentDetails,
    isLoading: isPurchaseIntentLoading,
    isError: isPurchaseIntentError
  } = usePurchaseIntentDetails(sessionId);

  const totalCost =
    purchaseIntentDetails?.order_summary.total_cost.toString() || '';
  const basketSummary = purchaseIntentDetails?.order_summary.items || [];
  const isEsimDataAvailable = dataState.isReady && esimData;
  const isPendingState = !dataState.isReady && !dataState.error;
  const isEsimDataUnavailable =
    dataState.isReady || (dataState.simPollingTimedOut && !isEsimDataAvailable);

  return (
    <div className="mx-auto max-w-[827px]">
      <div className="flex flex-col gap-2">
        <ConditionalWrapper className="rounded border-none px-6 pt-6 shadow-none lg:p-6">
          <InstructionBaseWrapper dataState={dataState} type="desktop">
            <div className="flex flex-wrap items-center gap-4 lg:gap-8">
              {dataState.error && (
                <Alert message={'We could not generate qr code.'} />
              )}
              {isEsimDataAvailable && (
                <QRCode
                  className="border-[#43CCBC]!"
                  qrcode={esimData.qr_code_base64}
                  size="large"
                />
              )}
              {isPendingState && <QrCodeSkeleton size="large" />}
              <InfoGrid />
            </div>
            <Divider className="my-6" />
            <VideoInstructions />
          </InstructionBaseWrapper>
        </ConditionalWrapper>
        <ConditionalWrapper className="mt-4 rounded border-none px-6 pt-6 shadow-none lg:p-6">
          <InstructionStepDesktop />
        </ConditionalWrapper>
        <Divider className="mx-auto block w-[calc(100%-48px)] lg:hidden" />
        <ConditionalWrapper className="rounded border-none px-6 shadow-none lg:my-4 lg:p-6">
          {isEsimDataUnavailable && <AccountAccessSection />}
        </ConditionalWrapper>
        <Divider className="mx-auto block w-[calc(100%-48px)] lg:hidden" />
        <ConditionalWrapper className="rounded border-none px-6 shadow-none lg:mb-4 lg:p-6">
          <h2 className="mb-6">Need some help?</h2>
          <HelpSection />
        </ConditionalWrapper>
        <Divider className="mx-auto block w-[calc(100%-48px)] lg:hidden" />
        <ConditionalWrapper className="rounded border-none px-6 shadow-none lg:p-6">
          {isPurchaseIntentError && (
            <Alert
              variant="error"
              message="We could not load your order summary."
            />
          )}
          {isPurchaseIntentLoading && !isPurchaseIntentError ? (
            <LoadingSpinner />
          ) : (
            <AfterPaymentOrderSummary
              basketSummary={basketSummary}
              totalCost={totalCost}
            />
          )}
          <Divider />
          <div className="my-4">
            <p className="text-xxxs inline">Full </p>
            <TermsModalButton />
            <p className="text-xxxs inline"> apply.</p>
          </div>
        </ConditionalWrapper>
      </div>
    </div>
  );
}
