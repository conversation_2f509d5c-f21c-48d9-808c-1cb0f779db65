import { Divider } from '@/components/divider/divider';
import { VideoInstructions } from '@/src/deliveroo/app/signup/payment/_components/video-instructions';
import { HelpSection } from '@/src/deliveroo/app/signup/payment/_components/help-section';
import { TermsModalButton } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/terms-modal-button';
import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { AfterPaymentOrderSummary } from '@/src/deliveroo/app/signup/payment/order-confirmation/_components/after-payment-order-summary';
import { checkIsIos, checkIsSafari } from '@/utils/helpers';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { usePurchaseIntentDetails } from '@/hooks/usePurchaseIntentDetails';
import { Alert } from '@/components/alert/alert';
import { LoadingSpinner } from '@/icons/icons';
// import { useSignupProgress } from '@/context/signup-progress-context/signup-progress-tracked-routes';

export function MobileOrderConfirmationFooter({
  showViewAccountButton
}: {
  showViewAccountButton: boolean;
}) {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id') || '';
  const {
    purchaseIntentDetails,
    isLoading: isPurchaseIntentLoading,
    isError: isPurchaseIntentError
  } = usePurchaseIntentDetails(sessionId);

  const totalCost =
    purchaseIntentDetails?.order_summary.total_cost.toString() || '';
  const basketSummary = purchaseIntentDetails?.order_summary.items || [];
  // const { resetProgress } = useSignupProgress();

  const {
    headingRef,
    buttonWrapperRef,
    placeholderRef,
    isSticky,
    isInitialized
  } = useStickyButton();

  return (
    <div data-testid="mobile-order-confirmation-footer" className="px-6 pt-2">
      {!(checkIsIos() && checkIsSafari()) && (
        <>
          <Divider className="mb-4" />
          <VideoInstructions />
        </>
      )}
      <Divider className="mb-4" />
      <h2 ref={headingRef} className="mb-4">
        Access your account
      </h2>
      <div className="flex flex-wrap items-center gap-2">
        <p className="lg:basis-2/3">
          Go to your account to manage all of your SIM plans and billing, get
          help and add new SIM plans.
        </p>

        <div className="relative w-full lg:w-fit">
          <div
            ref={placeholderRef}
            className="w-full lg:w-fit"
            style={{ visibility: isSticky ? 'visible' : 'hidden' }}
          />

          <div
            ref={buttonWrapperRef}
            className={`w-full transition-none lg:w-fit ${
              isSticky
                ? 'fixed right-0 bottom-0 left-0 z-50 px-3 pb-3'
                : 'absolute top-0 right-0 left-0'
            }`}
            style={{ opacity: isInitialized ? 1 : 0 }}
          >
            {showViewAccountButton && (
              <Link
                href={`${ROUTES_CONFIG['dashboard-esim'].path}?tab=account-billing`}
                className="text-bold text-secondary bg-primary mt-2 ml-auto block w-full min-w-[215px] p-3 text-center font-bold lg:mt-0 lg:w-fit"
                // onClick={() => resetProgress()}
              >
                View my account
              </Link>
            )}
          </div>
        </div>
      </div>
      <Divider className="mb-4" />
      <h2 className="mb-6">Need some help?</h2>
      <HelpSection />
      <Divider className="mb-4" />
      {isPurchaseIntentError && (
        <Alert
          variant="error"
          message="We could not load your order summary."
        />
      )}
      {isPurchaseIntentLoading && !isPurchaseIntentError ? (
        <LoadingSpinner />
      ) : (
        <AfterPaymentOrderSummary
          basketSummary={basketSummary}
          totalCost={totalCost}
        />
      )}
      <Divider />
      <div className="my-4 mb-8">
        <p className="text-xxxs inline underline">Full </p>
        <TermsModalButton />
        <p className="text-xxxs inline underline"> apply.</p>
      </div>
    </div>
  );
}

function useStickyButton() {
  const [isSticky, setIsSticky] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const headingRef = useRef<HTMLHeadingElement>(null);
  const buttonWrapperRef = useRef<HTMLDivElement>(null);
  const placeholderRef = useRef<HTMLDivElement>(null);
  const triggerPointRef = useRef<number>(0);

  useLayoutEffect(() => {
    if (!buttonWrapperRef.current || !headingRef.current) return;

    const headingRect = headingRef.current.getBoundingClientRect();
    triggerPointRef.current = headingRect.top + window.scrollY;

    const buttonRect = buttonWrapperRef.current.getBoundingClientRect();
    if (placeholderRef.current) {
      placeholderRef.current.style.height = `${buttonRect.height}px`;
    }

    setIsInitialized(true);
  }, []);

  useEffect(() => {
    if (!isInitialized) return;

    const handleScroll = () => {
      const scrollY = window.scrollY;
      const viewportHeight = window.innerHeight;

      const scrolledPastHeading =
        scrollY + viewportHeight > triggerPointRef.current;

      setIsSticky(scrolledPastHeading);
    };

    handleScroll();

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isInitialized]);

  return {
    isSticky,
    headingRef,
    buttonWrapperRef,
    placeholderRef,
    isInitialized
  };
}
