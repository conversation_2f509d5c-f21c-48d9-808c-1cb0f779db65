'use client';

import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { Suspense, useEffect, useTransition } from 'react';
import { useCheckSessionStatus } from '@/hooks/useCheckSessionStatus';
import { Loader } from '@/components/loader/loader';
import { RequireAuth } from '@/components/require-auth/require-auth';
import { useAuth } from '@/auth/hooks/use-auth';
import { PaymentVerificationLoader } from '@/src/deliveroo/app/dashboard/_components/payment-verification-loader';
// import { useSignupProgress } from '@/context/signup-progress-context/signup-progress-tracked-routes';

function PaymentReturn() {
  const router = useRouter();
  // const { updateProgress } = useSignupProgress();
  const { isAuthenticated } = useAuth();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  const sessionId = searchParams.get('session_id');

  const { sessionStatus, isSessionPending, isSessionError } =
    useCheckSessionStatus(sessionId);

  const isOrderComplete = sessionStatus?.status === 'complete';
  const isPaymentSuccessful = sessionStatus?.payment_status === 'paid';
  const isSessionComplete = isOrderComplete && isPaymentSuccessful;

  // const { saveCurrentStep } = useCurrentSignupStep({
  //   requiresAuth: true,
  //   brand: 'deliveroo'
  // });

  useEffect(() => {
    if (!isSessionComplete) return;
    // saveCurrentStep();
    // updateProgress({ isPaymentCompleted: true });

    if (isAuthenticated) {
      // need additional check !
      //   startTransition(() => {
      //     router.replace(
      //       `${ROUTES_CONFIG['dashboard-esim'].path}?purchase-confirmation=true`
      //     );
      //   });
      // } else {
      startTransition(() => {
        router.replace(
          `${ROUTES_CONFIG['order-confirmation'].path}?session_id=${sessionId}`
        );
      });
    }
  }, [
    isSessionComplete,
    sessionId,
    isAuthenticated,
    router,
    // saveCurrentStep,
    startTransition
    // updateProgress
  ]);

  useEffect(() => {
    if (isSessionError) {
      startTransition(() => {
        router.replace(`${ROUTES_CONFIG['payment'].path}?error=payment_failed`);
      });
    }
  }, [isSessionError, router, startTransition]);

  if (isSessionPending) {
    return <PaymentVerificationLoader message="Verifying card details" />;
  }

  if (isPending) {
    const pendingStateMessage = isSessionComplete
      ? 'Redirecting to confirmation…'
      : 'Taking you back to payment…';
    return <PaymentVerificationLoader message={pendingStateMessage} />;
  }

  return <PaymentVerificationLoader />;
}

export default function PaymentReturnPage() {
  return (
    <RequireAuth<typeof ROUTES_CONFIG>
      loadingComponent={<Loader />}
      redirectTo={ROUTES_CONFIG['plan-selection'].path}
    >
      <Suspense fallback={<PaymentVerificationLoader />}>
        <PaymentReturn />
      </Suspense>
    </RequireAuth>
  );
}
