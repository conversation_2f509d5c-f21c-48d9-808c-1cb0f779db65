import React from 'react';

export function PaymentVerificationLoader({
  message = 'Verifying card details'
}: {
  message?: string;
}) {
  return (
    <div
      className="flex h-screen w-full flex-col items-center justify-center"
      aria-live="polite"
      aria-atomic="true"
    >
      <div
        className="mb-4 size-9 animate-spin rounded-full border-3 border-current border-t-transparent"
        role="status"
        aria-hidden="true"
      />
      <p className="text-[18px]">{message}</p>
      <div
        className="sr-only"
        role="status"
        aria-live="assertive"
        aria-busy="true"
      >
        {message}
      </div>
    </div>
  );
}
