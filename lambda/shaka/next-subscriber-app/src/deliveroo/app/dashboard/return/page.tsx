import { Suspense } from 'react';
import { PaymentVerificationLoader } from '@/src/deliveroo/app/dashboard/_components/payment-verification-loader';
import ReturnClient from '@/src/deliveroo/app/dashboard/esim/@accountbilling/_components/return-client';

export default function Page() {
  return (
    <Suspense
      fallback={<PaymentVerificationLoader message="Verifying card details" />}
    >
      <ReturnClient />
    </Suspense>
  );
}
