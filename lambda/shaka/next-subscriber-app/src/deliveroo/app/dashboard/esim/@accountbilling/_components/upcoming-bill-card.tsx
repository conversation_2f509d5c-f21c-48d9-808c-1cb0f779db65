import { Divider } from '@/components/divider/divider';
import { PlainCard } from '@/components/plain-card/plain-card';
import { FullDetailsButton } from '@/components/full-details-button/full-details-button';
import { GenericModal } from '@/components/generic-modal/generic-modal';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';

// move to api call
interface Bill {
  id: string;
  period: string;
  amount: number;
  dueDate: string;
  status: 'warning' | 'overdue' | 'paid';
  paymentMethod: {
    type: 'card';
    lastDigits: string;
    // probably not needed
    isAutomatic: boolean;
  };
}

// dummy data
const upcomingBills: Bill[] = [
  {
    id: '1',
    period: 'February 2025',
    amount: 34,
    dueDate: '22/08/25',
    status: 'warning',
    paymentMethod: {
      type: 'card',
      lastDigits: '2199',
      isAutomatic: true
    }
  }
];

const statusColors = {
  warning: 'bg-warning-border',
  overdue: 'bg-error-border',
  paid: 'bg-success-border'
};

function BillSummary({ bill }: { bill: Bill }) {
  return (
    <div>
      <p className="text-xxxs">{bill.period}</p>
      <strong className="block text-xs">£{bill.amount}</strong>
    </div>
  );
}

function BillStatus({ bill }: { bill: Bill }) {
  return (
    <div className="col-start-2 flex items-center gap-2 justify-self-end lg:justify-self-start">
      <div
        className={`${statusColors[bill.status]} h-2 w-2 rounded-full`}
      ></div>
      <p className="text-xxxs inline">Due {bill.dueDate}</p>
    </div>
  );
}

function PaymentDetails({ bill }: { bill: Bill }) {
  if (!bill.paymentMethod.isAutomatic) {
    return null;
  }

  return (
    <div className="col-span-2 col-start-2 row-start-2 justify-self-end lg:col-span-3 lg:col-start-1 xl:col-span-1 xl:col-start-3 xl:row-start-1">
      <p className="text-xxxs text-right font-bold xl:text-left">
        Paid automatically from {bill.paymentMethod.type} ending{' '}
        {bill.paymentMethod.lastDigits}
      </p>
    </div>
  );
}

function BillActions({ bill }: { bill: Bill }) {
  return (
    <FullDetailsButton
      className="col-start-2 mt-2 ml-auto lg:col-start-3 lg:mt-0 xl:col-start-4"
      text="View details"
    >
      {({ isOpen, setIsOpen }) =>
        isOpen && (
          <GenericModal
            isOpen={isOpen}
            setIsOpen={setIsOpen}
            modalTitle={`Bill for ${bill.period}`}
            modalDescription={`Amount: £${bill.amount}, Due: ${bill.dueDate}`}
          />
        )
      }
    </FullDetailsButton>
  );
}

function BillItem({ bill }: { bill: Bill }) {
  return (
    <>
      <div className="grid grid-cols-2 grid-rows-[1fr_auto_auto] place-items-start gap-y-0 lg:grid-cols-[110px_2fr_auto] lg:grid-rows-[1fr_auto] lg:gap-x-4 xl:grid-cols-[110px_1fr_1fr_1fr] xl:grid-rows-1">
        <BillSummary bill={bill} />
        <BillStatus bill={bill} />
        <PaymentDetails bill={bill} />
        <BillActions bill={bill} />
      </div>
      <Divider className="mb-6 block lg:hidden" />
    </>
  );
}

export function UpcomingBillCard() {
  // api call via hook here
  const noUpcomingBills = upcomingBills.length === 0;
  return (
    <PlainCard
      as="article"
      className="mb-3 rounded border-none p-4 pb-0 shadow-none lg:pb-4"
    >
      {noUpcomingBills ? (
        <FormAlert
          variant="info"
          messages={["You have no upcoming bills"]}
          dismissible={false}
        />
      ) : (
        <>
          <header>
            <h3>Upcoming bills</h3>
          </header>
          <Divider className="mt-2" />
          {upcomingBills.map((bill) => (
            <BillItem key={bill.id} bill={bill} />
          ))}
        </>
      )}
    </PlainCard>
  );
}
