'use client';

import React from 'react';
import { UserPaymentDetailsCard } from '@/src/deliveroo/app/dashboard/esim/@accountbilling/_components/payment-details-card';
import { UpcomingBillCard } from '@/src/deliveroo/app/dashboard/esim/@accountbilling/_components/upcoming-bill-card';
import { UserPaidBillsList } from '@/src/deliveroo/app/dashboard/esim/@accountbilling/_components/paid-bills-list';
import { UserDetailsCard } from '@/src/deliveroo/app/dashboard/esim/@accountbilling/_components/details-card';

export default function AccountBilling() {
  // const { saveCurrentStep } = useCurrentSignupStep({
  //   requiresAuth: true,
  //   brand: 'deliveroo'
  // });

  return (
    <section className="grid grid-cols-1 gap-y-8 md:gap-x-6 md:gap-y-0 lg:grid-cols-[minmax(0,1.3fr)_minmax(0,0.7fr)] lg:gap-x-8">
      <div>
        <h2 className="mt-4 mb-2">Your bills</h2>
        <UpcomingBillCard />
        <UserPaidBillsList />
      </div>
      <div>
        <h2 className="mt-4 mb-2">Your details</h2>
        <UserDetailsCard />
        <h2 className="mt-8 mb-2">Your payment details</h2>

        <UserPaymentDetailsCard />
      </div>
    </section>
  );
}
