'use client';

import React, { useEffect, useTransition } from 'react';
import { PaymentVerificationLoader } from '@/src/deliveroo/app/dashboard/_components/payment-verification-loader';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCheckSessionStatus } from '@/hooks/useCheckSessionStatus';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';

export default function ReturnClient() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id');
  const [isPending, startTransition] = useTransition();
  const { sessionStatus, isSessionPending, isSessionError } =
    useCheckSessionStatus(sessionId);

  const { payment_status, status } = sessionStatus || {};
  const isSessionCompleted = status === 'complete';
  const isUpdateSuccessful = payment_status === 'no_payment_required';

  useEffect(() => {
    if (!isSessionCompleted || !isUpdateSuccessful) return;

    startTransition(() => {
      router.replace(`${ROUTES_CONFIG['card-detail'].path}&success=true`);
    });
  }, [isSessionCompleted, isUpdateSuccessful, router, startTransition]);

  useEffect(() => {
    if (isSessionError) {
      startTransition(() => {
        router.replace(`${ROUTES_CONFIG['card-detail'].path}&success=false`);
      });
    }
  }, [isSessionError, router, startTransition]);

  if (isSessionPending) {
    return <PaymentVerificationLoader message="Verifying card details" />;
  }

  if (isPending) {
    const pendingStateMessage = isSessionCompleted
      ? 'Redirecting to confirmation…'
      : 'Taking you back to payment…';
    return <PaymentVerificationLoader message={pendingStateMessage} />;
  }

  return <PaymentVerificationLoader />;
}
