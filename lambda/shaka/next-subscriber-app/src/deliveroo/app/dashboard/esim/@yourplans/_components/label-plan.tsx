import { Divider } from '@/components/divider/divider';
import Button from '@/components/button/button';
import React, { useState } from 'react';
import { useLabelPlan } from '@/hooks/useLabelPlan';
import {
  flattenValidationErrors,
  standariseNetworkError
} from '@/utils/helpers';
import { Alert } from '@/components/alert/alert';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { validatePlanNameForm } from '@/schemas/schemas';
import { useCurrentPlanId } from '@/context/current-plan-context';

export function LabelPlan() {
  const currentPlanId = useCurrentPlanId();
  const [planNameError, setPlanNameError] = useState('');

  const {
    labelPlan,
    isPending,
    error: mutationError,
    isSuccess
  } = useLabelPlan();

  // move to useLabelPlan hook ??
  const handlePlanNameChange = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    setPlanNameError('');

    const formData = new FormData(e.currentTarget);
    const planName = formData.get('planName') as string;

    const result = validatePlanNameForm(planName.trim());

    if (result.success) {
      if (result.data && currentPlanId) {
        labelPlan({ subscriptionId: currentPlanId, label: result.data });
      }
    } else {
      const formattedErrors = result.error?.format() || {};
      const [flatErrors] = flattenValidationErrors(formattedErrors);
      setPlanNameError(flatErrors);
    }
  };

  const getErrorMessage = () => {
    if (planNameError) return planNameError;
    if (mutationError) {
      const [networkError] = standariseNetworkError(mutationError);
      return networkError;
    }
    return '';
  };

  const error = getErrorMessage();
  return (
    <>
      {isSuccess ? (
        <FormAlert
          className="mb-4 lg:my-6"
          title=""
          variant="deliveroo"
          messages={[
            `message: Plan name has been updated successfully!. You may now close this window.`
          ]}
          dismissible={false}
        />
      ) : (
        <form onSubmit={handlePlanNameChange}>
          <label className="sr-only" htmlFor="planName">
            Plan name
          </label>
          <input
            type="text"
            name="planName"
            id="planName"
            placeholder="Main plan"
            className={`input mt-2 ${planNameError ? 'border-2 border-[var(--color-error)]' : ''}`}
            onChange={() => setPlanNameError('')}
          />
          {error && (
            <Alert
              variant="error"
              message={error}
              align="left"
              className="my-4"
            />
          )}
          <Divider className="mt-4" />
          <Button
            isLoading={isPending}
            disabled={isPending}
            className="w-full"
            variant="primary"
          >
            Confirm
          </Button>
        </form>
      )}
    </>
  );
}
