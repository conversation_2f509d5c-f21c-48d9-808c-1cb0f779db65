import React, { useEffect, useState } from 'react';
import { PlainCard } from '@/components/plain-card/plain-card';
import Image from 'next/image';
import stripeIcon from '@/public/images/stripe-icon.png';
import Button from '@/components/button/button';
import { GenericModal } from '@/components/generic-modal/generic-modal';
import { useCheckout, PaymentElement } from '@stripe/react-stripe-js';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { LoadingSpinner } from '@/icons/icons';
import { useUrlParamModal } from '@/hooks/useParamModal';
import { useCreateSetupSession } from '@/hooks/useCreateSetupSession';
import { ConfirmError } from '@stripe/stripe-js';
import { ROUTES_CONFIG } from '@/src/deliveroo/routes/route-config';
import { StripeProvider } from '@/lib/stripe/stripe-provider';
import {
  PAYMENT_CONSTANTS,
  STRIPE_ELEMENTS_OPTIONS
} from '@/lib/stripe/stripe-theme-config';

const returnUrl = ROUTES_CONFIG['card-detail-change-return'].path;

export function UserPaymentDetailsCard() {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const {
    isOpen: isResultModalOpen,
    paramValue: successParam,
    closeModal: closeResultModal
  } = useUrlParamModal('success');
  const isSuccess = successParam === 'true';

  const { createSetupSession, setupSession, isPending, error } =
    useCreateSetupSession();

  useEffect(() => {
    createSetupSession(returnUrl);
  }, [createSetupSession]);

  return (
    <PlainCard as="article" className="mb-12 rounded border-none shadow-none">
      <PaymentMethodDisplay
        lastFourDigits="2199"
        onEditClick={() => setIsEditModalOpen(true)}
      />

      <PaymentEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        setupSession={setupSession}
        isPending={isPending}
        error={error}
      />

      <PaymentUpdateResultModal
        isOpen={isResultModalOpen}
        onClose={closeResultModal}
        isSuccess={isSuccess}
      />
    </PlainCard>
  );
}

interface PaymentEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  setupSession: { clientSecret: string } | undefined;
  isPending: boolean;
  error?: Error | null;
}

export function PaymentEditModal({
  isOpen,
  onClose,
  setupSession,
  isPending,
  error
}: PaymentEditModalProps) {
  return (
    <StripeProvider
      clientSecret={setupSession?.clientSecret}
      mode={PAYMENT_CONSTANTS.PAYMENT_MODE}
      options={{
        appearance: STRIPE_ELEMENTS_OPTIONS.appearance
      }}
    >
      <GenericModal isOpen={isOpen} setIsOpen={onClose}>
        <PaymentFormContainer error={error} isPending={isPending} />
      </GenericModal>
    </StripeProvider>
  );
}

interface PaymentFormContainerProps {
  error?: Error | null;
  isPending: boolean;
}

export function PaymentFormContainer({
  error,
  isPending
}: PaymentFormContainerProps) {
  const { confirm } = useCheckout();
  const [submitting, setSubmitting] = useState(false);
  const [confirmError, setConfirmError] = useState<ConfirmError | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setConfirmError(null);
    const result = await confirm();
    if (result.type === 'error') {
      setConfirmError(result.error);
    }
    setSubmitting(false);
  };

  if (error) {
    return (
      <FormAlert
        className="mt-4"
        singleMessageStyles="text-text"
        title={PAYMENT_CONSTANTS.SETUP_ERROR_TITLE}
        variant="error"
        messages={[`Error: ${error.message}`]}
      />
    );
  }

  if (isPending) {
    return <PaymentFormLoading />;
  }

  return (
    <StripePaymentForm
      onSubmit={handleSubmit}
      isSubmitting={submitting}
      submitError={confirmError}
    />
  );
}

interface StripePaymentFormProps {
  onSubmit: (e: React.FormEvent) => void;
  isSubmitting: boolean;
  submitError?: ConfirmError | null;
  children?: React.ReactNode;
}

export function StripePaymentForm({
  onSubmit,
  isSubmitting,
  submitError,
  children
}: StripePaymentFormProps) {
  return (
    <form onSubmit={onSubmit}>
      <PaymentElement />
      {children}
      <div className="mt-6">
        {submitError && (
          <FormAlert
            variant="error"
            className="mb-4"
            singleMessageStyles="text-text"
            title={PAYMENT_CONSTANTS.PAYMENT_ERROR_TITLE}
            messages={submitError.message}
          />
        )}
      </div>
      <Button
        className="mx-auto mb-6 block w-full"
        disabled={isSubmitting}
        isLoading={isSubmitting}
        variant="primary"
      >
        {isSubmitting
          ? PAYMENT_CONSTANTS.PROCESSING_TEXT
          : PAYMENT_CONSTANTS.SUBMIT_BUTTON_TEXT}
      </Button>
    </form>
  );
}

interface PaymentUpdateResultModalProps {
  isOpen: boolean;
  onClose: () => void;
  isSuccess: boolean;
}

export function PaymentUpdateResultModal({
  isOpen,
  onClose,
  isSuccess
}: PaymentUpdateResultModalProps) {
  return (
    <GenericModal isOpen={isOpen} setIsOpen={() => onClose()}>
      <div className="p-4">
        <FormAlert
          singleMessageStyles="text-text"
          variant={isSuccess ? 'deliveroo' : 'error'}
          title={
            isSuccess
              ? PAYMENT_CONSTANTS.SUCCESS_TITLE
              : PAYMENT_CONSTANTS.ERROR_TITLE
          }
          messages={
            isSuccess
              ? PAYMENT_CONSTANTS.SUCCESS_MESSAGE
              : PAYMENT_CONSTANTS.ERROR_MESSAGE
          }
          dismissible={false}
        />
        <Button className="mt-4 w-full" variant="primary" onClick={onClose}>
          Close
        </Button>
      </div>
    </GenericModal>
  );
}

interface PaymentMethodDisplayProps {
  lastFourDigits: string;
  onEditClick: () => void;
}

export function PaymentMethodDisplay({
  lastFourDigits,
  onEditClick
}: PaymentMethodDisplayProps) {
  return (
    <>
      <div className="bg-gray-subtle text-default mb-4 rounded p-3">
        <p className="text-text">
          {PAYMENT_CONSTANTS.CARD_PREFIX} **** **** **** {lastFourDigits}
        </p>
      </div>
      <div className="text-default flex flex-wrap items-center justify-between gap-2">
        <div className="flex grow items-center gap-3">
          <p className="text-text">{PAYMENT_CONSTANTS.POWERED_BY_TEXT}</p>
          <Image width={74} height={31} src={stripeIcon} alt="Stripe icon" />
        </div>
        <Button onClick={onEditClick} variant="secondary">
          {PAYMENT_CONSTANTS.EDIT_BUTTON_TEXT}
        </Button>
      </div>
    </>
  );
}

interface PaymentFormLoadingProps {
  message?: string;
}

export function PaymentFormLoading({
  message = PAYMENT_CONSTANTS.SETUP_LOADING_TEXT
}: PaymentFormLoadingProps) {
  return (
    <div className="flex flex-col items-center justify-center py-8">
      <LoadingSpinner />
      <p className="mt-4">{message}</p>
    </div>
  );
}
