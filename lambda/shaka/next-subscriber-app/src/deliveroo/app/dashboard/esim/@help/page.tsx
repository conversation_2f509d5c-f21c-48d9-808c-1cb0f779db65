'use client';

import { PlainCard } from '@/components/plain-card/plain-card';
import React, { useState } from 'react';
import { Divider } from '@/components/divider/divider';
import { ChevronRight, SearchIcon } from '@/icons/icons';
import { helpSectionCardStatus } from '@/src/uswitch/utils/constants';
import { Chip } from '@/src/uswitch/app/dashboard/_components/chip';
import { OnlineStatus } from '@/src/uswitch/app/dashboard/_components/online-status';
import Button from '@/components/button/button';

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState('');
  return (
    <section className="grid grid-cols-1 gap-y-8 md:gap-x-6 md:gap-y-0 lg:grid-cols-[minmax(0,1.25fr)_minmax(0,0.75fr)] lg:gap-x-8">
      <div className="mt-4">
        <h1 className="text-base">How can we help?</h1>
        <label className="relative mt-5 mb-7 block" htmlFor="search">
          <span className="sr-only">Search</span>
          <input
            className="input"
            id="search"
            name="search"
            type="search"
            placeholder="My internet isn’t working"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            // onKeyDown={(e) => {
            //   if (e.key === 'Enter') {
            //     setSearchQuery(e.target.value);
            //   }
            // }}
          />
          <button aria-label="Search" className="cursor-pointer">
            {!searchQuery && (
              <SearchIcon className="absolute top-1/2 right-4 -translate-y-1/2" />
            )}
          </button>
        </label>

        <PlainCard as="article" className="rounded border-none p-4 shadow-none">
          <h2>Helpful articles</h2>
          <Divider className="mt-2 mb-6" />
          <AccordionItem />
          <AccordionItem />
          <AccordionItem />
          <AccordionItem />
          <AccordionItem />
        </PlainCard>

        <Button
          className="my-8 lg:mb-12"
          variant="secondary"
          onClick={() => {}}
        >
          See all articles
        </Button>
      </div>
      <div className="mb-10 lg:mb-0">
        <h2 className="mt-4 mb-2 text-base">Email support</h2>
        <PlainCard
          as="article"
          className="mb-3 rounded border-none p-0 shadow-none"
        >
          <HelpSectionCard
            status="available"
            email="<EMAIL>"
            text="Responds within around 10 minutes"
            onClick={() => {}}
            buttonText="Email us"
          />
        </PlainCard>
        <h2 className="mt-4 mb-2 text-base">Online chat</h2>
        <PlainCard
          as="article"
          className="mb-3 rounded border-none p-0 shadow-none"
        >
          <HelpSectionCard
            status="available"
            text="Responds within around 5 minutes"
            onClick={() => {}}
            buttonText="Chat with us"
          />
        </PlainCard>
      </div>
    </section>
  );
}

interface AccordionItemProps {
  status: string;
  text: string;
  email?: string;
  buttonText?: string;
  onClick?: () => void;
}

function HelpSectionCard({
  status,
  text,
  email,
  buttonText,
  onClick
}: AccordionItemProps) {
  const config = helpSectionCardStatus[status];
  return (
    <>
      <div className="rounded-2 flex flex-col gap-4 p-4">
        {/*is this status comes from api ?*/}
        <div className="flex flex-wrap items-center justify-between gap-y-2">
          <OnlineStatus color={config.color} text={config.text} />
          <Chip>{text}</Chip>
        </div>

        <Divider className="mt-1! mb-2! lg:-mt-2! lg:mb-2!" />
        <div
          className={`grid grid-cols-1 gap-4 ${email ? 'lg:flex lg:flex-wrap lg:gap-x-2 lg:gap-y-2 xl:flex-nowrap' : 'lg:grid-cols-1'} lg:gap-6`}
        >
          {email && <p className="text-default">{email}</p>}
          {email ? (
            <a
              href={`mailto:${email}`}
              className="hover:bg-secondary-hover hover:border-primary hover:text-primary text-default w-full basis-xs cursor-pointer rounded-[2px] border-2 bg-white p-3 text-center font-bold lg:w-[144px] lg:justify-self-end"
            >
              {buttonText}
            </a>
          ) : (
            <Button
              variant="secondary"
              className="basis-xs lg:w-[144px] lg:justify-self-end"
              onClick={onClick}
            >
              {buttonText}
            </Button>
          )}
        </div>
      </div>
    </>
  );
}

function AccordionItem() {
  return (
    <details className="group border-border mt-6 border-b pb-4">
      <summary className="flex w-full cursor-pointer items-center justify-between text-left transition-colors duration-150">
        <p className="text-[18px] font-bold">How do I install an eSIM?</p>
        <ChevronRight className="chevron" />
      </summary>
      <div className="content px-4 py-2 text-pretty">
        Installing an eSIM is easy! proceeding.
      </div>
    </details>
  );
}
