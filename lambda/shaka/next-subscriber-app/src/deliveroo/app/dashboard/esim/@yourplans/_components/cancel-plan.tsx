import { Divider } from '@/components/divider/divider';
import Button from '@/components/button/button';
import React from 'react';
import { useCancelPlan } from '@/hooks/useCancelPlan';
import { Alert } from '@/components/alert/alert';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import useGetCurrentlySelectedPlan from '@/hooks/useGetCurrentlySelectedPlan';
import { checkForNextMonth } from '@/utils/helpers';

interface CancelPlanProps {
  setIsOpen: (isOpen: boolean) => void;
}

export function CancelPlan({ setIsOpen }: CancelPlanProps) {
  const currentSubscription = useGetCurrentlySelectedPlan();

  const {
    cancelPlan,
    isPending,
    error: mutationError,
    isSuccess
  } = useCancelPlan();

  const handleCancel = () => setIsOpen(false);

  const handleConfirm = () => {
    if (!currentSubscription) return;
    cancelPlan(currentSubscription.id);
  };

  return (
    <>
      {isSuccess ? (
        <CancelPlanConfirmation />
      ) : (
        <CancelPlanForm
          onCancel={handleCancel}
          onConfirm={handleConfirm}
          isPending={isPending}
          error={mutationError}
        />
      )}
    </>
  );
}

interface CancelPlanFormProps {
  onCancel: () => void;
  onConfirm: () => void;
  isPending: boolean;
  error: Error | null;
}

function CancelPlanForm({
  onCancel,
  onConfirm,
  isPending,
  error
}: CancelPlanFormProps) {
  return (
    <>
      <CancelPlanWarningText />
      <Divider className="my-4" />
      <CancelPlanActionButtons
        onCancel={onCancel}
        onConfirm={onConfirm}
        isPending={isPending}
      />
      <CancelPlanErrorAlert error={error} />
    </>
  );
}

function CancelPlanWarningText() {
  const currentYear = new Date().getFullYear();
  const nextMonth = checkForNextMonth(new Date());
  const cancellationDate = `01/${nextMonth}/${currentYear}`;

  return (
    <>
      <p>
        Once you confirm your cancellation, this plan will remain active until{' '}
        <span className="font-bold">{cancellationDate}</span> and then will be
        cancelled.
      </p>
      <br />
      <p>
        To keep your number for your next provider please text &quot;PAC&quot;
        to 65075.
      </p>
    </>
  );
}

interface CancelPlanActionButtonsProps {
  onCancel: () => void;
  onConfirm: () => void;
  isPending: boolean;
}

function CancelPlanActionButtons({
  onCancel,
  onConfirm,
  isPending
}: CancelPlanActionButtonsProps) {
  return (
    <div className="flex flex-wrap gap-4">
      <Button
        onClick={onCancel}
        variant="secondary"
        className="grow hover:border-black hover:text-black! sm:basis-[calc(50%-8px)]"
      >
        I’ve changed my mind
      </Button>
      <Button
        isLoading={isPending}
        disabled={isPending}
        onClick={onConfirm}
        variant="primary"
        className="grow sm:basis-[calc(50%-8px)]"
      >
        Cancel my plan
      </Button>
    </div>
  );
}

interface CancelPlanErrorAlertProps {
  error: Error | null;
}

function CancelPlanErrorAlert({ error }: CancelPlanErrorAlertProps) {
  if (!error?.message) return null;

  return (
    <Alert
      variant="error"
      message={error.message}
      align="left"
      className="my-4"
    />
  );
}

function CancelPlanConfirmation() {
  const currentYear = new Date().getFullYear();
  const nextMonth = checkForNextMonth(new Date());
  const cancellationDate = `01/${nextMonth}/${currentYear}`;

  return (
    <FormAlert
      dismissible={false}
      variant="deliveroo"
      title="Your plan has been cancelled"
      titleStyles=""
      messagesStyles="space-y-4"
      messages={[
        `yourPlanRemainsActiveUntil: You can still use your plan until ${cancellationDate}.`,
        `lastPaymentDate: The last payment had already been taken.`,
        `keepYourNumber: To keep your number for your next provider please text “PAC” to 65075.`
      ]}
    />
  );
}
