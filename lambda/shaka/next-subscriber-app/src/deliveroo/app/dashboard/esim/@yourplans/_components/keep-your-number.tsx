import { usePACFormSubmission } from '@/hooks/usePACFormSubmission';
import { CodeCard } from '@/src/deliveroo/app/signup/number-porting/_components/code-info-grid';
import { PACIcon, TextMessageIcon } from '@/icons/icons';
import { Divider } from '@/components/divider/divider';
import { NumberPortingFormContainer } from '@/components/number-porting-form/number-porting-form';
import Button from '@/components/button/button';
import React from 'react';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import useGetCurrentlySelectedPlan from '@/hooks/useGetCurrentlySelectedPlan';

export function KeepYourNumber() {
  const currentSubscription = useGetCurrentlySelectedPlan();
  const {
    handleSubmit,
    isPending,
    errors,
    formData,
    setErrors,
    successMessage
  } = usePACFormSubmission(currentSubscription?.id);

  return (
    <>
      {successMessage ? (
        <FormAlert
          className="mb-4 lg:my-6"
          title=""
          variant="deliveroo"
          messages={[
            `message: Your PAC code has been successfully sent. You may now close this window.`
          ]}
          dismissible={false}
        />
      ) : (
        <>
          <div className="mb-4 grid grid-cols-1 gap-4 lg:grid-cols-2">
            <CodeCard
              icon={<TextMessageIcon fill="#C2E9E9" />}
              title="Text “PAC” to 65075"
              className="items-center"
            />
            <CodeCard
              icon={<PACIcon fill="#C2E9E9" />}
              title="Enter your code below"
              className="items-center"
            />
          </div>
          <p>
            You’ll receive your PAC code from your old network in a few minutes
            which you need to enter below.
          </p>
          <Divider className="my-4" />
          <h2>Enter your PAC code</h2>
          <NumberPortingFormContainer
            setErrors={setErrors}
            errors={errors}
            handleSubmit={handleSubmit}
            formData={formData}
          />
          <Divider className="my-4" />
          <Button
            isLoading={isPending}
            aria-disabled={isPending}
            disabled={isPending}
            form="pacForm"
            variant="primary"
            className="w-full"
          >
            Keep your number
          </Button>
        </>
      )}
    </>
  );
}
