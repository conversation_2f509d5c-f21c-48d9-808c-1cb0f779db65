import { CogFilledIcon, SignalIcon } from '@/icons/icons';

export const planOrderItems = [
  {
    id: 1,
    name: 'Unlimited SIM plan',
    price: 15,
    description: {
      unlimitedTexts: 'Unlimited UK texts',
      unlimitedMinutes: 'Unlimited UK minutes',
      unlimitedData: 'Unlimited UK data',
      euRoaming: 'European roaming up to 20GB per month.',
      euRoamingLimit: 'Limited to a 10 days per trip.',
      fiveG: '5G network speeds (VodafoneThree network)',
      cancelAnytime: 'Cancel whenever you want',
      keepNumber: 'Switch and keep your number in one simple step'
    }
  }
];

export const plans = [
  { feature: 'Unlimited UK texts, calls and data' },
  { feature: '5G coverage on VodafoneThree network' },
  { feature: 'Keep your number' },
  { feature: 'No contract' },
  { feature: 'Exclusive Deliveroo rider discount' },
  { feature: 'Instant set up with eSIM' },
  { feature: 'EU roaming' }
];

export const helpSectionCardStatus = {
  available: {
    color: 'bg-success-border',
    text: 'Available'
  }
};

export const tabConfig = [
  { value: 'your-plans', label: 'Your plans' },
  { value: 'account-billing', label: 'Account & billing' },
  { value: 'help', label: 'Help' }
] as const;

export const infoGrid = [
  {
    icon: CogFilledIcon,
    title: 'Navigate to Settings',
    text: 'Go to Mobile Service > Pick this SIM (likely labelled as “Secondary” or “Travel”) > Mobile Data Network'
  },
  {
    icon: SignalIcon,
    title: 'Change APN to "gamma"',
    text: 'Delete “three.co.uk” in the APN field in the “Mobile data” section and type in "gamma"'
  }
];
