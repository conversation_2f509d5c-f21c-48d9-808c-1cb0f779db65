export const ROUTES_CONFIG = {
  login: {
    path: '/login',
    name: 'Login'
  },
  signup: {
    path: '/signup',
    name: 'Signup'
  },
  'forgot-password': {
    path: '/forgot-password',
    name: 'Forgot password'
  },
  'sign-in': {
    path: '',
    name: 'Sign in'
  },
  register: {
    path: '/signup/register',
    name: 'Register'
  },
  otp: {
    path: '/signup/otp',
    name: 'OTP'
  },
  'order-confirmation': {
    path: '/signup/payment/order-confirmation',
    name: 'Order Confirmation'
  },
  'payment-error': {
    path: '/signup/payment/failure',
    name: 'Error'
  },
  'payment-return': {
    path: '/signup/payment/return',
    name: 'Payment Return'
  },
  'card-detail-change-return': {
    path: '/dashboard/return',
    name: 'Payment Return'
  },
  'card-detail': {
    path: '/dashboard/esim?tab=account-billing',
    name: 'Payment Return'
  },
  dashboard: {
    path: '/dashboard',
    name: 'Dashboard'
  },
  'dashboard-overview': {
    path: '/dashboard/overview',
    name: 'Overview'
  },
  'dashboard-track': {
    path: '/dashboard/track',
    name: 'Track'
  },
  'dashboard-esim': {
    path: '/dashboard/esim',
    name: 'eSIM'
  },
  'dashboard-manage': {
    path: '/dashboard/manage',
    name: 'Manage'
  },
  'plan-selection': {
    path: '/signup/plan-selection',
    name: 'Explore Plans'
  },
  'number-porting': {
    path: '/signup/number-porting',
    name: 'Number Porting'
  },
  payment: {
    path: '/signup/payment',
    name: 'Payment'
  },
  'iphone-installation': {
    path: '/signup/iphone-installation',
    name: 'iPhone Installation'
  }
} as const;

export type Route = (typeof ROUTES_CONFIG)[keyof typeof ROUTES_CONFIG]['path'];
