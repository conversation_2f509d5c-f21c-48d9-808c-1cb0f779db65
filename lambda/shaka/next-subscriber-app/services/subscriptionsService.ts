import { AxiosClient } from '@/lib/axios-client';
import { API_ENDPOINTS } from '@/auth/api/endpoints';
import { Email } from '@/schema/schema';

import {
  PlanNameFormData,
  SubscriptionsApiResponse,
  SubscriptionsSchema
} from '@/schemas/schemas';

// todo: for each service add types file

interface PacCode {
  status: 'pending' | 'not-set' | 'active' | 'completed';
  number: string | null;
  expected_on: number | null;
}

interface PlanChange {
  id: number;
  status: 'in_progress' | 'completed' | 'cancelled';
  change_type: 'downgrade' | 'upgrade' | 'cancel_change' | 'cancellation';
  target_plan_id: number | null;
  target_plan_change_id: number | null;
  subscriber_status_display: string;
}

interface Notifications {
  show_number_transfer: boolean;
  show_number_porting_progress: boolean;
  show_update_apn: boolean;
  show_set_up_esim: boolean;
}

export interface Plan {
  id: number;
  correlation_id: string;
  bg: number;
  plan_id: number | null;
  price: string | null;
  next_bill_amount: string;
  next_bill_date_epoch: number;
  potential_cancellation_date_epoch: number;
  title: string | null;
  sim_activation_status: 'active' | 'not-assigned' | 'pending';
  sim_serial_fragment: string | null;
  data: any | null; // Could be refined further if structure is known
  voice: any | null; // Could be refined further if structure is known
  sms: any | null; // Could be refined further if structure is known
  subscription_status: 'active' | 'inactive' | 'cancelled';
  pac_code_status: 'pending' | 'not-set' | 'active' | 'completed';
  pac_code: PacCode;
  porting_expected_date: string | null;
  porting_phone_number: string;
  self_activated: boolean;
  can_upgrade: boolean;
  can_downgrade: boolean;
  can_cancel: boolean;
  can_cancel_change: boolean;
  plan_changes: PlanChange[];
  latest_plan_change: PlanChange | null;
  sim_type: 'esim' | 'physical';
  bolt_ons: any[]; // Could be refined further if structure is known
  roaming_bolt_on: any | null; // Could be refined further if structure is known
  roaming_bolt_on_eu: any | null; // Could be refined further if structure is known
  notifications: Notifications;
  phone_number: string | null;
  user_subscription_name: string;
  esim_status: string;
}

interface NextReward {
  id: number;
  title: string;
  days_left: number;
  img: string;
}

interface RoamingEsimData {
  used: number;
  left: number;
}

interface RoamingEsim {
  id: number;
  custom_name: string;
  zone: string;
  expires: string;
  data: RoamingEsimData;
  status: 'active' | 'expired' | 'pending';
  is_unlimited: boolean;
  esim_status: string;
  correlation_id: string | null;
  show_esim_settings: boolean;
}

export interface Subscriber {
  id: number;
  email: string;
  name: string;
  date_of_birth: string | null;
  phone_number: string;
  plans: Plan[];
  address: string;
  is_verified: boolean;
  join_date: string;
  send_marketing: boolean;
  sim_fragment: string | null;
  referral_applied: boolean;
  points: number;
  referral_bonus: string;
  next_rewards: NextReward[];
  onboarding_details: any | null;
  roaming_esims: RoamingEsim[];
  last_4_digits: string;
  async_api_token: string;
}

export const subscriptionsService = {
  getSubscriber: async (apiClient: AxiosClient): Promise<Subscriber> => {
    return await apiClient.get(
      `http://localhost:8000/s/api/v1/1/${API_ENDPOINTS.subscriptions.subscriber}`
    );
  },
  getSubscriptions: async (
    apiClient: AxiosClient
  ): Promise<SubscriptionsApiResponse> => {
    // const response = await apiClient.get(
    //   `http://localhost:8000/s/api/v1/1/${API_ENDPOINTS.subscriptions.base}`
    // );
    // const response = await apiClient.get(API_ENDPOINTS.subscriptions.base);
    // const result = SubscriptionsSchema.safeParse(response);

    // create a function and parse all api responses
    // if (!result.success) {
    //   console.error('Invalid plans data:', result.error.format());
    //   throw new Error('Received invalid plans data from the server');
    // }

    // return result.data || [];
    return await apiClient.get(API_ENDPOINTS.subscriptions.base);
  },
  // getPurchaseDetails: async (apiClient: AxiosClient): Promise<any> => {
  //   return await apiClient.get(API_ENDPOINTS.plans.base);
  // },
  shareEsim: async (
    apiClient: AxiosClient,
    currentPlanId: number,
    email: Email
  ): Promise<any> => {
    return await apiClient.post(
      API_ENDPOINTS.subscriptions.shareEsim(currentPlanId.toString()),
      {
        email
      }
    );
  },
  labelPlan: async (
    apiClient: AxiosClient,
    subscriptionId: number,
    label: PlanNameFormData
  ): Promise<any> => {
    return await apiClient.put(
      `${API_ENDPOINTS.subscriptions.labelPlan(subscriptionId.toString())}`,
      {
        plan_name: label
      }
    );
  },
  cancelPlan: async (
    apiClient: AxiosClient,
    subscriptionId: number
  ): Promise<any> => {
    return await apiClient.post(API_ENDPOINTS.subscriptions.cancel(), {
      subscription_id: subscriptionId,
      target_plan_id: null
    });
  },
  resumePlan: async (
    apiClient: AxiosClient,
    migrationId: number
  ): Promise<any> => {
    return await apiClient.delete(
      API_ENDPOINTS.subscriptions.resume(migrationId.toString())
    );
  },
  addExtraRoamingData: async (
    apiClient: AxiosClient,
    currentPlanId: number,
    roamingData: number
  ): Promise<any> => {
    return await apiClient.post(
      API_ENDPOINTS.subscriptions.addExtraRoamingData(currentPlanId.toString()),
      {
        roamingData
      }
    );
  }
};
