import Modal from '@/components/modal/modal';
import { CloseIcon } from '@/icons/icons';
import React from 'react';

interface GenericModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  modalTitle?: string;
  modalDescription?: string;
  modalSize?: string;
  titleStyles?: string;
  children?: React.ReactNode;
  disableCloseButton?: boolean;
}

export function GenericModal({
  isOpen,
  setIsOpen,
  modalTitle,
  modalDescription,
  modalSize = 'lg:max-w-2xl',
  titleStyles = '',
  children,
  disableCloseButton
}: GenericModalProps) {
  return isOpen ? (
    <Modal onOpenChange={setIsOpen} open={isOpen}>
      <Modal.Overlay />
      <Modal.Content className={`w-full rounded-lg p-6 ${modalSize}`}>
        <div className="mb-4 flex justify-end">
          {!disableCloseButton && (
            <Modal.Close>
              <CloseIcon />
            </Modal.Close>
          )}
        </div>
        <Modal.Title className={`mb-6 text-xl font-semibold ${titleStyles}`}>
          {modalTitle}
        </Modal.Title>
        {modalDescription ? (
          <Modal.Description>{modalDescription}</Modal.Description>
        ) : (
          children
        )}
      </Modal.Content>
    </Modal>
  ) : null;
}
