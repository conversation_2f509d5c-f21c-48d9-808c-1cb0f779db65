import { useMutation } from '@tanstack/react-query';
import { paymentService } from '@/services/paymentService';
import { useAuth } from '@/auth/hooks/use-auth';

export function useCreateSetupSession() {
  const { apiClient } = useAuth();

  const {
    mutate: createSetupSession,
    data: setupSession,
    isPending,
    error
  } = useMutation({
    mutationFn: (returnUrl: string) =>
      paymentService.createSetupSession(apiClient, returnUrl)
  });

  return {
    createSetupSession,
    setupSession,
    isPending,
    error
  };
}
