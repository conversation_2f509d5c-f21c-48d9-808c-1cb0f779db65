import { useQuery } from '@tanstack/react-query';
import { paymentService } from '@/services/paymentService';
import { paymentKeys } from '@/query-keys/query-keys';
import { useAuth } from '@/auth/hooks/use-auth';

export function useCheckSessionStatus(sessionId: string | null) {
  const { apiClient } = useAuth();
  const {
    data: sessionStatus,
    isPending,
    error
  } = useQuery({
    queryKey: [paymentKeys.sessionID, sessionId],
    queryFn: () =>
      paymentService.checkoutSessionStatus(apiClient, sessionId || ''),
    retry: false,
    staleTime: 1000
  });

  return {
    sessionStatus,
    isSessionPending: isPending,
    isSessionError: error
  };
}
