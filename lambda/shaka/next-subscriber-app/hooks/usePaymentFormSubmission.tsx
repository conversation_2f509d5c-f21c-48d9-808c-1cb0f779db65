import { useMutation } from '@tanstack/react-query';
import { useCheckout } from '@stripe/react-stripe-js';
import { useRouter } from 'next/navigation';
import { AxiosError } from 'axios';
import { ConfirmError } from '@stripe/stripe-js';
import { standariseNetworkError } from '@/utils/helpers';
import { useState, useCallback } from 'react';

export function usePaymentFormSubmission() {
  const router = useRouter();
  const { confirm } = useCheckout();
  const [errors, setErrors] = useState<string[]>([]);

  const { mutateAsync: submitPayment, isPending } = useMutation({
    mutationFn: async () => {
      const result = await confirm();
      if (result.type === 'error') {
        throw result.error;
      }
      return result;
    },
    onSuccess: () => {
      router.push('/payment/return');
    },
    onError: (error: ConfirmError | AxiosError) =>
      setErrors(standariseNetworkError(error))
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await submitPayment();
  };

  const handleExpressCheckout = useCallback(async () => {
    try {
      setErrors([]);
      await submitPayment();
    } catch (error) {
      setErrors(standariseNetworkError(error));
    }
  }, [submitPayment]);

  const handleExpressCancel = useCallback(() => {
    setErrors([]);
  }, []);

  const handleExpressLoadError = useCallback(
    (event: { error: { message?: string } }) => {
      const errorMessage =
        event.error.message || 'Failed to load express payment method';
      setErrors([errorMessage]);
    },
    []
  );

  return {
    handleSubmit,
    handleExpressCheckout,
    handleExpressCancel,
    handleExpressLoadError,
    isPending,
    errors
  };
}
