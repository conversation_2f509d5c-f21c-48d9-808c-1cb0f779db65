import { useRouter } from 'next/navigation';
import { useSearchParams } from 'next/navigation';

export function useUrlParamModal(paramName: string) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const paramValue = searchParams.get(paramName);
  const isOpen = !!paramValue;

  const closeModal = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.delete(paramName);
    const newParams = params.toString();
    router.replace(newParams ? `?${newParams}` : window.location.pathname);
  };

  return {
    isOpen,
    paramValue,
    closeModal
  };
}
