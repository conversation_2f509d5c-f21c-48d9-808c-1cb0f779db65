import { useMutation } from '@tanstack/react-query';
import { subscriptionsService } from '@/services/subscriptionsService';
import { useAuth } from '@/auth/hooks/use-auth';

export function useCancelPlan() {
  const { apiClient } = useAuth();

  const {
    mutate: cancelPlan,
    isPending,
    error,
    isError,
    isSuccess
  } = useMutation({
    mutationFn: async (subscriptionId: number) =>
      subscriptionsService.cancelPlan(apiClient, subscriptionId)
  });

  return {
    cancelPlan,
    isPending,
    error,
    isError,
    isSuccess
  };
}
