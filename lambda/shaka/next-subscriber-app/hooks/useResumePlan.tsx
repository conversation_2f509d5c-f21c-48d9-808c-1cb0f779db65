import { useMutation } from '@tanstack/react-query';
import { subscriptionsService } from '@/services/subscriptionsService';
import { useAuth } from '@/auth/hooks/use-auth';

export function useResumePlan() {
  const { apiClient } = useAuth();

  const {
    mutate: resumePlan,
    isPending,
    error,
    isError,
    isSuccess
  } = useMutation({
    mutationFn: async (migrationId: number) =>
      subscriptionsService.resumePlan(apiClient, migrationId)
  });

  return {
    resumePlan,
    isPending,
    error,
    isError,
    isSuccess
  };
}
