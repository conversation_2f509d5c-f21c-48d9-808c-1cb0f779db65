import { PaymentStatus, SessionStatus, StripeResponse } from "@/types/stripe";
import api from ".";

export const getClientSecretSignUp = (
  planId: number,
  returnURL?: string,
): Promise<StripeResponse> =>
  api
    .post(
      `/plans/sign-up/${planId}/${
        returnURL ? `?ui_mode=custom&return_path=${returnURL}` : ""
      }`,
    )
    .then((res) => res.data);

export const checkSessionStatus = (
  sessionId: string,
): Promise<{
  payment_status: PaymentStatus;
  status: SessionStatus;
  expires_at: number;
}> =>
  api
    .get(`/checkout/session/status/?session_id=${sessionId}`)
    .then((res) => res.data);

export const getClientSecretCard =
  (returnURL?: string) => (): Promise<string> =>
    api
      .post(
        "subscription/change-card/" +
          (returnURL ? `?return_path=${returnURL}&is_query=true` : ""),
      )
      .then((res) => res.data.client_secret);
