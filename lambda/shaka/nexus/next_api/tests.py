from decimal import Decimal
from unittest.mock import patch
import time

from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APITestCase
from rest_framework import status
from core.models import Plan, Client, Provider, PlanComponent, PlanComponentOffering, RoamingEsimPackage, Subscriber, Subscription, Sim, SimSubscriptionAssignment, NumberAssignment, EuRoamingTracking
from .models import FamilyPlanLink, TravelAddon, PlanTravelAddon


class NextApiTestCase(APITestCase):  # pylint: disable=too-many-instance-attributes
    def setUp(self):
        self.provider = Provider.objects.create(
            name="demo",
            unlimited_data_actual_limit=100,
            is_demo=True
        )
        self.client_obj = Client.objects.create(
            name="Test Client",
            provider=self.provider,
            openid_state_secret="test_secret"
        )
        self.data_component = PlanComponent.objects.create(
            description="Data Component",
            dimension=PlanComponent.Dimension.DATA,
            provider=self.provider,
            default_price=10.00,
            default_cost=5.00,
            max_limit=50
        )

        self.voice_component = PlanComponent.objects.create(
            description="Voice Component",
            dimension=PlanComponent.Dimension.VOICE,
            provider=self.provider,
            default_price=10.00,
            default_cost=5.00,
            max_limit=0
        )

        self.sms_component = PlanComponent.objects.create(
            description="SMS Component",
            dimension=PlanComponent.Dimension.SMS,
            provider=self.provider,
            default_price=10.00,
            default_cost=5.00,
            max_limit=0
        )
        self.data_offering = PlanComponentOffering.objects.create(
            client=self.client_obj,
            plan_component=self.data_component
        )
        self.voice_offering = PlanComponentOffering.objects.create(
            client=self.client_obj,
            plan_component=self.voice_component
        )
        self.sms_offering = PlanComponentOffering.objects.create(
            client=self.client_obj,
            plan_component=self.sms_component
        )
        self.main_plan = Plan.objects.create(
            name="Unlimited",
            client=self.client_obj,
            provider=self.provider,
            price=Decimal('15.00'),
            status=Plan.PlanStatuses.ACTIVE,
            data_component_offering=self.data_offering,
            voice_component_offering=self.voice_offering,
            sms_component_offering=self.sms_offering,
            custom_data_limit=0,
            base_eu_roaming_days=20
        )

        self.family_plan = Plan.objects.create(
            name="40GB",
            client=self.client_obj,
            provider=self.provider,
            price=Decimal('10.00'),
            status=Plan.PlanStatuses.ACTIVE,
            data_component_offering=self.data_offering,
            voice_component_offering=self.voice_offering,
            sms_component_offering=self.sms_offering,
            custom_data_limit=40,
            base_eu_roaming_days=10
        )
        self.roaming_package_1gb = RoamingEsimPackage.objects.create(
            name="Europe 1GB",
            client=self.client_obj,
            cost=Decimal('0.50'),
            data_limit_gb=Decimal('1.00'),
            price=Decimal('1.00'),
            days=7,
            enabled=True
        )

        self.roaming_package_2gb = RoamingEsimPackage.objects.create(
            name="Europe 2GB",
            client=self.client_obj,
            cost=Decimal('1.00'),
            data_limit_gb=Decimal('2.00'),
            price=Decimal('2.00'),
            days=7,
            enabled=True
        )
        self.travel_addon_1gb = TravelAddon.objects.create(
            name="Europe Extra 1GB (for unlimited)",
            region="Europe",
            price=Decimal('1.00'),
            is_active=True
        )
        self.travel_addon_1gb.roaming_esim_packages.add(self.roaming_package_1gb)

        self.travel_addon_2gb = TravelAddon.objects.create(
            name="Europe Extra 2GB (for unlimited)",
            region="Europe",
            price=Decimal('2.00'),
            is_active=True
        )
        self.travel_addon_2gb.roaming_esim_packages.add(self.roaming_package_2gb)

        self.family_link = FamilyPlanLink.objects.create(
            main_plan=self.main_plan,
            sub_plan=self.family_plan,
            price_override=Decimal('8.00'),
            is_active=True
        )

        self.plan_addon_1gb = PlanTravelAddon.objects.create(
            plan=self.main_plan,
            travel_addon=self.travel_addon_1gb,
            is_active=True
        )

        self.plan_addon_2gb = PlanTravelAddon.objects.create(
            plan=self.main_plan,
            travel_addon=self.travel_addon_2gb,
            is_active=True
        )

        self.family_addon_1gb = PlanTravelAddon.objects.create(
            plan=self.family_plan,
            travel_addon=self.travel_addon_1gb,
            price_override=Decimal('100.00'),
            is_active=True
        )
        self.test_subscriber = Subscriber.objects.create(
            email="<EMAIL>",
            name="Test User",
            client=self.client_obj,
            cognito_username="test_user",
            join_date=timezone.now()
        )

        self.jwt_patcher = patch('next_api.authentication.jwt_is_valid')
        self.mock_jwt_valid = self.jwt_patcher.start()
        self.mock_jwt_valid.return_value = True

        self.username_patcher = patch('next_api.authentication.get_username_from_jwt')
        self.mock_get_username = self.username_patcher.start()
        self.mock_get_username.return_value = 'test_user'

    def tearDown(self):
        self.jwt_patcher.stop()
        self.username_patcher.stop()

    def get_auth_headers(self):
        return {'HTTP_AUTHORIZATION': 'Bearer test_token'}


class PlansApiTestCase(NextApiTestCase):

    def test_health_check_endpoint(self):
        url = reverse('next_api:health-check')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'healthy')
        self.assertEqual(response.data['service'], 'next_api')

    def test_plans_list_without_authentication(self):
        url = reverse('next_api:user-plans-list', args=[self.client_obj.pk])
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsInstance(response.data, list)
        self.assertEqual(len(response.data), 2)

    def test_plan_json_structure(self):
        url = reverse('next_api:user-plans-list', args=[self.client_obj.pk])
        response = self.client.get(url, **self.get_auth_headers())
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        main_plan_data = None
        for plan in response.data:
            if plan['name'] == 'Unlimited':
                main_plan_data = plan
                break

        self.assertIsNotNone(main_plan_data)
        expected_keys = ['id', 'name', 'allowances', 'travel_addons', 'price', 'family_plans']
        for key in expected_keys:
            self.assertIn(key, main_plan_data)
        allowances = main_plan_data['allowances']
        expected_allowance_keys = ['data', 'calls', 'texts', 'europe_data']
        for key in expected_allowance_keys:
            self.assertIn(key, allowances)
        self.assertEqual(allowances['data'], 'unlimited')
        self.assertEqual(allowances['calls'], 'unlimited')
        self.assertEqual(allowances['texts'], 'unlimited')
        self.assertEqual(allowances['europe_data'], 20)
        travel_addons = main_plan_data['travel_addons']
        self.assertIsInstance(travel_addons, list)
        self.assertEqual(len(travel_addons), 2)
        addon = travel_addons[0]
        expected_addon_keys = ['id', 'name', 'region', 'allowances', 'price']
        for key in expected_addon_keys:
            self.assertIn(key, addon)

        family_plans = main_plan_data['family_plans']
        self.assertIsInstance(family_plans, list)
        self.assertEqual(len(family_plans), 1)
        family_plan = family_plans[0]
        expected_family_keys = ['id', 'name', 'price', 'allowances', 'travel_addons']
        for key in expected_family_keys:
            self.assertIn(key, family_plan)
        self.assertEqual(family_plan['name'], '40GB')
        self.assertEqual(family_plan['price'], 8.0)
        self.assertEqual(family_plan['allowances']['data'], 40)
        self.assertEqual(family_plan['allowances']['europe_data'], 10)

    def test_client_specific_plans_endpoint(self):
        url = reverse('next_api:client-plans-list', kwargs={'client_id': self.client_obj.id})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsInstance(response.data, list)
        self.assertEqual(len(response.data), 2)

    def test_plan_detail_endpoint(self):
        url = reverse('next_api:client-plan-detail', kwargs={
            'client_id': self.client_obj.id,
            'plan_id': self.main_plan.id
        })
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], self.main_plan.id)
        self.assertEqual(response.data['name'], 'Unlimited')

    def test_travel_addon_pricing_override(self):
        url = reverse('next_api:user-plans-list', args=[self.client_obj.pk])
        response = self.client.get(url, **self.get_auth_headers())
        family_plan_data = None
        for plan in response.data:
            if plan['name'] == '40GB':
                family_plan_data = plan
                break

        self.assertIsNotNone(family_plan_data)
        travel_addons = family_plan_data['travel_addons']
        self.assertEqual(len(travel_addons), 1)

        addon = travel_addons[0]
        self.assertEqual(addon['price'], 100.0)

    def test_invalid_client_access(self):
        url = reverse('next_api:client-plans-list', kwargs={'client_id': 99999})
        response = self.client.get(url, **self.get_auth_headers())
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)


class SubscriptionNameUpdateTestCase(NextApiTestCase):
    def setUp(self):
        super().setUp()
        self.subscriber = Subscriber.objects.create(
            email="<EMAIL>",
            name="Test User",
            client=self.client_obj,
            cognito_username="test_user",
            join_date=timezone.now()
        )

        self.subscription = Subscription.objects.create(
            subscriber=self.subscriber,
            intended_plan=self.main_plan,
            status=Subscription.Statuses.ACTIVE,
            start_date="2024-01-01T00:00:00Z",
            user_subscription_name=""
        )

        self.other_client = Client.objects.create(
            name="Other Client",
            provider=self.provider,
            openid_state_secret="other_secret"
        )
        self.other_subscriber = Subscriber.objects.create(
            email="<EMAIL>",
            name="Other User",
            client=self.other_client,
            cognito_username="other_user",
            join_date=timezone.now()
        )
        self.other_subscription = Subscription.objects.create(
            subscriber=self.other_subscriber,
            intended_plan=self.main_plan,
            status=Subscription.Statuses.ACTIVE,
            start_date="2024-01-01T00:00:00Z",
            user_subscription_name=""
        )

    def test_subscription_name_update_success(self):
        url = reverse('next_api:subscription-name-update', kwargs={
            'client_id': self.client_obj.id,
            'subscription_id': self.subscription.id
        })
        data = {'name': 'My Custom Plan'}
        response = self.client.post(url, data, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['success'], True)
        self.assertEqual(response.data['subscription_id'], self.subscription.id)
        self.assertEqual(response.data['name'], 'My Custom Plan')
        self.subscription.refresh_from_db()
        self.assertEqual(self.subscription.user_subscription_name, 'My Custom Plan')

    def test_subscription_name_update_empty_name(self):
        """Test updating subscription name to empty string"""
        url = reverse('next_api:subscription-name-update', kwargs={
            'client_id': self.client_obj.id,
            'subscription_id': self.subscription.id
        })
        data = {'name': ''}
        response = self.client.post(url, data, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], '')
        self.subscription.refresh_from_db()
        self.assertEqual(self.subscription.user_subscription_name, '')

    def test_subscription_name_update_whitespace_trimmed(self):
        url = reverse('next_api:subscription-name-update', kwargs={
            'client_id': self.client_obj.id,
            'subscription_id': self.subscription.id
        })
        data = {'name': '  My Plan  '}
        response = self.client.post(url, data, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'My Plan')
        self.subscription.refresh_from_db()
        self.assertEqual(self.subscription.user_subscription_name, 'My Plan')

    def test_subscription_name_update_too_long(self):
        url = reverse('next_api:subscription-name-update', kwargs={
            'client_id': self.client_obj.id,
            'subscription_id': self.subscription.id
        })
        data = {'name': 'This name is way too long for the field limit'}
        response = self.client.post(url, data, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('cannot exceed 20 characters', response.data['error'])
        self.subscription.refresh_from_db()
        self.assertEqual(self.subscription.user_subscription_name, '')

    def test_subscription_name_update_exactly_20_chars(self):
        url = reverse('next_api:subscription-name-update', kwargs={
            'client_id': self.client_obj.id,
            'subscription_id': self.subscription.id
        })
        data = {'name': '12345678901234567890'}
        response = self.client.post(url, data, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], '12345678901234567890')

    def test_subscription_name_update_no_authentication(self):
        url = reverse('next_api:subscription-name-update', kwargs={
            'client_id': self.client_obj.id,
            'subscription_id': self.subscription.id
        })
        data = {'name': 'My Plan'}
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_subscription_name_update_invalid_subscription(self):
        url = reverse('next_api:subscription-name-update', kwargs={
            'client_id': self.client_obj.id,
            'subscription_id': 99999
        })
        data = {'name': 'My Plan'}
        response = self.client.post(url, data, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('not found or access denied', response.data['error'])

    def test_subscription_name_update_cross_client_access(self):
        url = reverse('next_api:subscription-name-update', kwargs={
            'client_id': self.client_obj.id,
            'subscription_id': self.other_subscription.id
        })
        data = {'name': 'Hacked Plan'}
        response = self.client.post(url, data, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('not found or access denied', response.data['error'])
        self.other_subscription.refresh_from_db()
        self.assertEqual(self.other_subscription.user_subscription_name, '')

    def test_subscription_name_update_wrong_client_id(self):
        url = reverse('next_api:subscription-name-update', kwargs={
            'client_id': self.other_client.id,
            'subscription_id': self.subscription.id
        })
        data = {'name': 'My Plan'}
        response = self.client.post(url, data, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Access denied for this client', response.data['error'])

    def test_subscription_name_update_missing_name_field(self):
        url = reverse('next_api:subscription-name-update', kwargs={
            'client_id': self.client_obj.id,
            'subscription_id': self.subscription.id
        })
        data = {}
        response = self.client.post(url, data, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], '')
        self.subscription.refresh_from_db()
        self.assertEqual(self.subscription.user_subscription_name, '')

class SubscriptionsListTestCase(NextApiTestCase):
    def setUp(self):
        super().setUp()
        self.subscription1 = Subscription.objects.create(
            subscriber=self.test_subscriber,
            intended_plan=self.main_plan,
            status=Subscription.Statuses.ACTIVE,
            start_date="2024-01-01T00:00:00Z",
            user_subscription_name="My Main Plan",
            billing_subscription_id="sub_test_1"
        )

        self.subscription2 = Subscription.objects.create(
            subscriber=self.test_subscriber,
            intended_plan=self.family_plan,
            status=Subscription.Statuses.ACTIVE,
            start_date="2024-02-01T00:00:00Z",
            user_subscription_name="Family Plan",
            billing_subscription_id="sub_test_2"
        )

        self.other_subscriber = Subscriber.objects.create(
            email="<EMAIL>",
            name="Other User",
            client=self.client_obj,
            cognito_username="other_user",
            join_date=timezone.now()
        )
        self.other_subscription = Subscription.objects.create(
            subscriber=self.other_subscriber,
            intended_plan=self.main_plan,
            status=Subscription.Statuses.ACTIVE,
            start_date="2024-01-01T00:00:00Z",
            user_subscription_name="Other Plan",
            billing_subscription_id="sub_other_1"
        )

    def test_subscriptions_list_success(self):
        url = reverse('next_api:client-subscriptions-list', kwargs={'client_id': self.client_obj.id})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsInstance(response.data, list)
        self.assertEqual(len(response.data), 2)

        subscription_ids = [sub['id'] for sub in response.data]
        self.assertIn(self.subscription1.id, subscription_ids)
        self.assertIn(self.subscription2.id, subscription_ids)

    def test_subscriptions_list_response_format(self):
        url = reverse('next_api:client-subscriptions-list', kwargs={'client_id': self.client_obj.id})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        subscription_data = response.data[0]

        expected_fields = [
            'id', 'subscriber', 'current_plan', 'current_sim', 'current_msisdn',
            'start_date', 'end_date', 'status', 'service_type',
            'current_billing_cycle_end', 'next_billing_cycle_start',
            'billing_cycle_period', 'reference_id', 'correlation_id', 'usage', 'roaming_usage'
        ]
        for field in expected_fields:
            self.assertIn(field, subscription_data)

        self.assertEqual(subscription_data['subscriber'], self.test_subscriber.id)

    def test_subscriptions_list_usage_structure(self):
        url = reverse('next_api:client-subscriptions-list', kwargs={'client_id': self.client_obj.id})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        subscription_data = response.data[0]

        self.assertIn('usage', subscription_data)
        usage = subscription_data['usage']

        self.assertIn('uk', usage)
        self.assertIn('europe', usage)

        uk_usage = usage['uk']
        self.assertIn('data', uk_usage)
        self.assertIn('voice', uk_usage)
        self.assertIn('sms', uk_usage)

        data_usage = uk_usage['data']
        self.assertIn('used', data_usage)
        self.assertIn('remaining', data_usage)

        self.assertIn('period_start', usage)
        self.assertIn('period_end', usage)
        self.assertIn('billing_cycle_period', usage)
        self.assertEqual(usage['billing_cycle_period'], 'monthly')

        self.assertIn('roaming_usage', subscription_data)
        roaming_usage = subscription_data['roaming_usage']
        self.assertIn('days_used', roaming_usage)
        self.assertIn('days_allowed', roaming_usage)

        self.assertIsInstance(roaming_usage['days_used'], int)
        self.assertIsInstance(roaming_usage['days_allowed'], int)

    def test_subscriptions_list_usage_no_sim_assigned(self):
        subscription_no_sim = Subscription.objects.create(
            subscriber=self.test_subscriber,
            intended_plan=self.main_plan,
            status=Subscription.Statuses.ACTIVE,
            start_date="2024-01-01T00:00:00Z",
            billing_subscription_id="sub_no_sim"
        )

        url = reverse('next_api:client-subscriptions-list', kwargs={'client_id': self.client_obj.id})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        no_sim_data = None
        for sub_data in response.data:
            if sub_data['id'] == subscription_no_sim.id:
                no_sim_data = sub_data
                break

        self.assertIsNotNone(no_sim_data)

        usage = no_sim_data['usage']
        self.assertEqual(usage['uk']['data']['used'], 0)
        self.assertIsNone(usage['uk']['data']['remaining'])
        self.assertEqual(usage['europe']['data']['used'], 0)
        self.assertIsNone(usage['europe']['data']['remaining'])

        self.assertIn('period_start', usage)
        self.assertIn('period_end', usage)
        self.assertEqual(usage['billing_cycle_period'], 'monthly')
        roaming_usage = no_sim_data['roaming_usage']
        self.assertEqual(roaming_usage['days_used'], 0)
        self.assertEqual(roaming_usage['days_allowed'], 7)

    def test_subscriptions_list_usage_unlimited_plan(self):
        unlimited_plan = Plan.objects.create(
            name="Unlimited Plan",
            client=self.client_obj,
            provider=self.provider,
            price=50.00,
            status=Plan.PlanStatuses.ACTIVE,
            data_component_offering=self.data_offering,
            voice_component_offering=self.voice_offering,
            sms_component_offering=self.sms_offering,
            custom_data_limit=0,
            custom_voice_limit=0,
            custom_sms_limit=0
        )
        subscription_unlimited = Subscription.objects.create(
            subscriber=self.test_subscriber,
            intended_plan=unlimited_plan,
            status=Subscription.Statuses.ACTIVE,
            start_date="2024-01-01T00:00:00Z",
            billing_subscription_id="sub_unlimited"
        )

        url = reverse('next_api:client-subscriptions-list', kwargs={'client_id': self.client_obj.id})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        unlimited_data = None
        for sub_data in response.data:
            if sub_data['id'] == subscription_unlimited.id:
                unlimited_data = sub_data
                break

        self.assertIsNotNone(unlimited_data)

        usage = unlimited_data['usage']
        self.assertIsNone(usage['uk']['data']['remaining'])
        self.assertIsNone(usage['uk']['voice']['remaining'])
        self.assertIsNone(usage['uk']['sms']['remaining'])

        self.assertIsInstance(usage['uk']['data']['used'], int)
        self.assertIsInstance(usage['uk']['voice']['used'], int)
        self.assertIsInstance(usage['uk']['sms']['used'], int)

    def test_subscriptions_list_usage_roaming_days_calculation(self):
        current_date = timezone.now()
        EuRoamingTracking.objects.create(
            subscription=self.subscription1,
            year=current_date.year,
            month=current_date.month,
            roaming_days=7
        )

        url = reverse('next_api:client-subscriptions-list', kwargs={'client_id': self.client_obj.id})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        sub1_data = None
        for sub_data in response.data:
            if sub_data['id'] == self.subscription1.id:
                sub1_data = sub_data
                break

        self.assertIsNotNone(sub1_data)
        roaming_usage = sub1_data['roaming_usage']
        self.assertEqual(roaming_usage['days_used'], 7)
        self.assertGreaterEqual(roaming_usage['days_allowed'], 0)

    def test_subscriptions_list_usage_sim_no_msisdn(self):
        sim_no_number = Sim.objects.create(
            serial_number="999888777666555444",
            status=Sim.SimStatuses.ACTIVE,
            esim_available_to=self.client_obj
        )
        subscription_no_msisdn = Subscription.objects.create(
            subscriber=self.test_subscriber,
            intended_plan=self.main_plan,
            status=Subscription.Statuses.ACTIVE,
            start_date="2024-01-01T00:00:00Z",
            billing_subscription_id="sub_no_msisdn"
        )

        SimSubscriptionAssignment.objects.create(
            sim=sim_no_number,
            subscription=subscription_no_msisdn,
            start_date=timezone.now()
        )

        url = reverse('next_api:client-subscriptions-list', kwargs={'client_id': self.client_obj.id})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        no_msisdn_data = None
        for sub_data in response.data:
            if sub_data['id'] == subscription_no_msisdn.id:
                no_msisdn_data = sub_data
                break

        self.assertIsNotNone(no_msisdn_data)
        usage = no_msisdn_data['usage']
        self.assertEqual(usage['uk']['data']['used'], 0)
        self.assertIsNone(usage['uk']['data']['remaining'])

    def test_subscriptions_list_no_authentication(self):
        url = reverse('next_api:client-subscriptions-list', kwargs={'client_id': self.client_obj.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_subscriptions_list_invalid_client_access(self):
        other_provider = Provider.objects.create(
            name="staging",
            unlimited_data_actual_limit=100,
            is_demo=True
        )
        other_client = Client.objects.create(
            name="Other Client",
            provider=other_provider,
            openid_state_secret="other_secret"
        )

        url = reverse('next_api:client-subscriptions-list', kwargs={'client_id': other_client.id})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_subscriptions_list_nonexistent_client(self):
        url = reverse('next_api:client-subscriptions-list', kwargs={'client_id': 99999})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_subscriptions_list_empty_result(self):
        Subscriber.objects.create(
            email="<EMAIL>",
            name="Empty User",
            client=self.client_obj,
            cognito_username="empty_user",
            join_date=timezone.now()
        )

        with patch('next_api.authentication.get_username_from_jwt') as mock_username:
            mock_username.return_value = 'empty_user'

            url = reverse('next_api:client-subscriptions-list', kwargs={'client_id': self.client_obj.id})
            response = self.client.get(url, **self.get_auth_headers())

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIsInstance(response.data, list)
            self.assertEqual(len(response.data), 0)

    def test_subscriptions_list_filters_by_subscriber(self):
        url = reverse('next_api:client-subscriptions-list', kwargs={'client_id': self.client_obj.id})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        for subscription_data in response.data:
            subscriber_id = subscription_data['subscriber']
            self.assertEqual(subscriber_id, self.test_subscriber.id)

        subscription_ids = [sub['id'] for sub in response.data]
        self.assertNotIn(self.other_subscription.id, subscription_ids)

class SimsListTestCase(NextApiTestCase):  # pylint: disable=too-many-instance-attributes
    def setUp(self):
        super().setUp()

        self.subscriber = Subscriber.objects.create(
            email="<EMAIL>",
            name="Test User",
            client=self.client_obj,
            cognito_username="test_user",
            join_date=timezone.now()
        )

        self.subscription1 = Subscription.objects.create(
            subscriber=self.subscriber,
            intended_plan=self.main_plan,
            status=Subscription.Statuses.ACTIVE,
            start_date="2024-01-01T00:00:00Z",
            billing_subscription_id="sub_test_1"
        )

        self.subscription2 = Subscription.objects.create(
            subscriber=self.subscriber,
            intended_plan=self.family_plan,
            status=Subscription.Statuses.ACTIVE,
            start_date="2024-02-01T00:00:00Z",
            billing_subscription_id="sub_test_2"
        )

        self.esim = Sim.objects.create(
            serial_number="123456789012345678",
            status=Sim.SimStatuses.ACTIVE,
            esim_data="LPA:1$sm-dp.example.com$ABC123",
            esim_available_to=self.client_obj
        )

        self.physical_sim = Sim.objects.create(
            serial_number="987654321098765432",
            status=Sim.SimStatuses.ACTIVE,
            esim_available_to=self.client_obj
        )

        self.inactive_sim = Sim.objects.create(
            serial_number="555666777888999000",
            status=Sim.SimStatuses.INACTIVE,
            esim_available_to=self.client_obj
        )

        SimSubscriptionAssignment.objects.create(
            sim=self.esim,
            subscription=self.subscription1,
            start_date=timezone.now()
        )

        SimSubscriptionAssignment.objects.create(
            sim=self.physical_sim,
            subscription=self.subscription2,
            start_date=timezone.now()
        )

        SimSubscriptionAssignment.objects.create(
            sim=self.inactive_sim,
            subscription=self.subscription1,
            start_date=timezone.now()
        )

        NumberAssignment.objects.create(
            sim=self.esim,
            phone_number="447123456789",
            start_date=timezone.now()
        )

        NumberAssignment.objects.create(
            sim=self.physical_sim,
            phone_number="447987654321",
            start_date=timezone.now()
        )

        NumberAssignment.objects.create(
            sim=self.inactive_sim,
            phone_number="447555666777",
            start_date=timezone.now()
        )

        self.other_provider = Provider.objects.create(
            name="staging",
            unlimited_data_actual_limit=100,
            is_demo=True
        )
        self.other_client = Client.objects.create(
            name="Other Client",
            provider=self.other_provider,
            openid_state_secret="other_secret"
        )
        self.other_subscriber = Subscriber.objects.create(
            email="<EMAIL>",
            name="Other User",
            client=self.other_client,
            cognito_username="other_user",
            join_date=timezone.now()
        )
        self.other_subscription = Subscription.objects.create(
            subscriber=self.other_subscriber,
            intended_plan=self.main_plan,
            status=Subscription.Statuses.ACTIVE,
            start_date="2024-01-01T00:00:00Z",
            billing_subscription_id="sub_other_1"
        )
        self.other_sim = Sim.objects.create(
            serial_number="111222333444555666",
            status=Sim.SimStatuses.ACTIVE,
            esim_available_to=self.other_client
        )
        SimSubscriptionAssignment.objects.create(
            sim=self.other_sim,
            subscription=self.other_subscription,
            start_date=timezone.now()
        )
        NumberAssignment.objects.create(
            sim=self.other_sim,
            phone_number="447111222333",
            start_date=timezone.now()
        )

    def tearDown(self):
        self.jwt_patcher.stop()
        self.username_patcher.stop()

    def test_sims_list_success(self):
        url = reverse('next_api:client-sims-list', kwargs={'client_id': self.client_obj.id})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        print(response.data)
        self.assertIsInstance(response.data, list)
        self.assertEqual(len(response.data), 3)

        sim_serial_numbers = [sim['serial_number'] for sim in response.data]
        self.assertIn(self.esim.serial_number, sim_serial_numbers)
        self.assertIn(self.physical_sim.serial_number, sim_serial_numbers)
        self.assertIn(self.inactive_sim.serial_number, sim_serial_numbers)

    def test_sims_list_response_format(self):
        url = reverse('next_api:client-sims-list', kwargs={'client_id': self.client_obj.id})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        sim_data = response.data[0]

        expected_fields = [
            'serial_number', 'status', 'activation_date',
            'current_msisdn', 'sim_type', 'service_type',
            'esim_data'
        ]
        for field in expected_fields:
            self.assertIn(field, sim_data)

    def test_sims_list_esim_data_format(self):
        url = reverse('next_api:client-sims-list', kwargs={'client_id': self.client_obj.id})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        esim_data = None
        for sim in response.data:
            if sim['serial_number'] == self.esim.serial_number:
                esim_data = sim
                break

        self.assertIsNotNone(esim_data)
        self.assertEqual(esim_data['sim_type'], 'esim')
        self.assertIsNotNone(esim_data['esim_data'])

    def test_sims_list_no_authentication(self):
        url = reverse('next_api:client-sims-list', kwargs={'client_id': self.client_obj.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_sims_list_invalid_client_access(self):
        url = reverse('next_api:client-sims-list', kwargs={'client_id': self.other_client.id})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_sims_list_nonexistent_client(self):
        url = reverse('next_api:client-sims-list', kwargs={'client_id': 99999})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_sims_list_subscriber_isolation(self):
        url = reverse('next_api:client-sims-list', kwargs={'client_id': self.client_obj.id})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        sim_serial_numbers = [sim['serial_number'] for sim in response.data]
        self.assertNotIn(self.other_sim.serial_number, sim_serial_numbers)

        for sim_data in response.data:
            sim = Sim.objects.get(serial_number=sim_data['serial_number'])
            assignments = sim.subscription_assignments.all()
            for assignment in assignments:
                self.assertEqual(assignment.subscription.subscriber, self.subscriber)

    def test_sims_list_empty_result(self):
        empty_subscriber = Subscriber.objects.create(
            email="<EMAIL>",
            name="Empty User",
            client=self.client_obj,
            cognito_username="empty_user",
            join_date=timezone.now()
        )

        with patch('next_api.views.get_subscriber_from_request') as mock_get_subscriber:
            mock_get_subscriber.return_value = empty_subscriber

            url = reverse('next_api:client-sims-list', kwargs={'client_id': self.client_obj.id})
            response = self.client.get(url, **self.get_auth_headers())

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIsInstance(response.data, list)
            self.assertEqual(len(response.data), 0)

class SimWaitForActivationTestCase(NextApiTestCase):
    def setUp(self):
        super().setUp()

        self.active_esim = Sim.objects.create(
            serial_number="123456789",
            status=Sim.SimStatuses.ACTIVE,
            esim_data="LPA:1$sm-dp.example.com$ABC123",
            esim_available_to=self.client_obj
        )

        self.inactive_esim = Sim.objects.create(
            serial_number="987654321",
            status=Sim.SimStatuses.INACTIVE,
            esim_data="LPA:1$sm-dp.example.com$DEF456",
            esim_available_to=self.client_obj
        )

        self.active_physical = Sim.objects.create(
            serial_number="555666777",
            status=Sim.SimStatuses.ACTIVE,
            dispatched_by=self.client_obj
        )

    @patch('next_api.authentication.validate_client_access')
    def test_active_esim_returns_immediately(self, mock_validate):
        mock_validate.return_value = self.client_obj

        url = reverse('next_api:sim-wait-for-activation', kwargs={'sim_id': self.active_esim.id})
        start_time = time.time()
        response = self.client.get(url, **self.get_auth_headers())
        end_time = time.time()

        self.assertLess(end_time - start_time, 1.0)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.data
        self.assertEqual(data['serial_number'], '123456789')
        self.assertEqual(data['status'], 'active')
        self.assertEqual(data['sim_type'], 'esim')
        self.assertEqual(data['esim_data'], 'LPA:1$sm-dp.example.com$ABC123')
        self.assertEqual(data['esim_code'], 'ABC123')
        self.assertEqual(data['esim_address'], 'sm-dp.example.com')

    @patch('next_api.authentication.validate_client_access')
    def test_active_physical_sim_returns_immediately(self, mock_validate):
        mock_validate.return_value = self.client_obj

        url = reverse('next_api:sim-wait-for-activation', kwargs={'sim_id': self.active_physical.id})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.data
        self.assertEqual(data['serial_number'], '555666777')
        self.assertEqual(data['status'], 'active')
        self.assertEqual(data['sim_type'], 'physical')
        self.assertIsNone(data['esim_data'])
        self.assertIsNone(data['esim_code'])
        self.assertIsNone(data['esim_address'])

    @patch('next_api.authentication.validate_client_access')
    def test_nonexistent_sim_returns_404(self, mock_validate):
        mock_validate.return_value = self.client_obj

        url = reverse('next_api:sim-wait-for-activation', kwargs={'sim_id': 99999})
        response = self.client.get(url, **self.get_auth_headers())

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
