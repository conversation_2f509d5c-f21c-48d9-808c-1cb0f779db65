from django.urls import path
from .views import PlansListView, PlanDetailView, ClientPlansView, HealthCheckView, SimWaitForActivationView, SubscriptionNameUpdateView, SubscriptionsListView, SimsListView, MigrateSubscriptionView, CancelSubscriptionMigrationView
app_name = 'next_api'

urlpatterns = [
    path('health/', HealthCheckView.as_view(), name='health-check'),
    path('clients/<int:client_id>/plans/', PlansListView.as_view(), name='client-plans-list'),
    path('clients/<int:client_id>/plans/<int:plan_id>/', PlanDetailView.as_view(), name='client-plan-detail'),
    path('<int:client_id>/plans/', ClientPlansView.as_view(), name='user-plans-list'),
    path('<int:client_id>/data/subscriptions/', SubscriptionsListView.as_view(), name='client-subscriptions-list'),
    path('<int:client_id>/data/sims/', SimsListView.as_view(), name='client-sims-list'),
    path('<int:client_id>/journey/migrate-subscription/', MigrateSubscriptionView.as_view(), name='migrate-subscription'),
    path('<int:client_id>/journey/migrate-subscription/<int:migration_id>', CancelSubscriptionMigrationView.as_view(), name='cancel-subscription-migration'),
    path('clients/<int:client_id>/subscriptions/<int:subscription_id>/name/', SubscriptionNameUpdateView.as_view(), name='subscription-name-update'),
    path('sims/<int:sim_id>/wait-for-activation/', SimWaitForActivationView.as_view(), name='sim-wait-for-activation'),
]
