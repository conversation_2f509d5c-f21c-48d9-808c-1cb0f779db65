from rest_framework import serializers
from core.models import Sim, PlanChange
from core.time_control import STRIPE_BILLING_TIME_CONTROL
from data_api.serializers import SimSerializer as DataApiSimSerializer, SubscriptionSerializer as DataApiSubscriptionSerializer, ReadonlySerializer
from .models import FamilyPlanLink, PlanTravelAddon


class TravelAddonSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    region = serializers.CharField()
    allowances = serializers.SerializerMethodField()
    price = serializers.DecimalField(max_digits=10, decimal_places=2)

    def create(self, validated_data):  # pylint: disable=unused-argument
        raise NotImplementedError("TravelAddonSerializer is read-only")

    def update(self, instance, validated_data):  # pylint: disable=unused-argument
        raise NotImplementedError("TravelAddonSerializer is read-only")

    def get_allowances(self, obj):
        travel_addon = obj.travel_addon if hasattr(obj, 'travel_addon') else obj
        total_data = travel_addon.total_data_gb

        return {
            "data": int(total_data) if total_data > 0 else "unlimited"
        }

    def to_representation(self, instance):
        if hasattr(instance, 'travel_addon'):
            travel_addon = instance.travel_addon
            price = instance.effective_price
        else:
            travel_addon = instance
            price = instance.price

        return {
            'id': travel_addon.id,
            'name': travel_addon.name,
            'region': travel_addon.region,
            'allowances': self.get_allowances(instance),
            'price': float(price)
        }


class FamilyPlanSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    price = serializers.DecimalField(max_digits=10, decimal_places=2)
    allowances = serializers.SerializerMethodField()
    travel_addons = serializers.SerializerMethodField()

    def create(self, validated_data):  # pylint: disable=unused-argument
        raise NotImplementedError("FamilyPlanSerializer is read-only")

    def update(self, instance, validated_data):  # pylint: disable=unused-argument
        raise NotImplementedError("FamilyPlanSerializer is read-only")

    def get_allowances(self, obj):
        plan = obj.sub_plan
        data_display = plan.data_limit_display_qty
        if data_display == 'unlimited':
            data = 'unlimited'
        else:
            data = int(plan.data_limit_gb) if plan.data_limit_gb else 0

        voice_display = plan.voice_limit_display_qty
        if voice_display == 'unlimited':
            voice = 'unlimited'
        else:
            voice = int(plan.voice_limit) if plan.voice_limit else 0

        sms_display = plan.sms_limit_display_qty
        if sms_display == 'unlimited':
            texts = 'unlimited'
        else:
            texts = int(plan.sms_limit) if plan.sms_limit else 0

        return {
            "data": data,
            "calls": voice,
            "texts": texts,
            "europe_data": plan.base_eu_roaming_days
        }

    def get_travel_addons(self, obj):
        plan = obj.sub_plan

        plan_travel_addons = PlanTravelAddon.objects.filter(
            plan=plan,
            is_active=True,
            travel_addon__is_active=True
        ).select_related('travel_addon').prefetch_related('travel_addon__roaming_esim_packages')

        return TravelAddonSerializer(plan_travel_addons, many=True).data

    def to_representation(self, instance):
        return {
            'id': instance.sub_plan.id,
            'name': instance.sub_plan.name,
            'price': float(instance.price_override),
            'allowances': self.get_allowances(instance),
            'travel_addons': self.get_travel_addons(instance)
        }


class PlanSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    allowances = serializers.SerializerMethodField()
    travel_addons = serializers.SerializerMethodField()
    price = serializers.DecimalField(max_digits=10, decimal_places=2)
    family_plans = serializers.SerializerMethodField()

    def create(self, validated_data):  # pylint: disable=unused-argument
        raise NotImplementedError("PlanSerializer is read-only")

    def update(self, instance, validated_data):  # pylint: disable=unused-argument
        raise NotImplementedError("PlanSerializer is read-only")

    def get_allowances(self, obj):
        data_display = obj.data_limit_display_qty
        if data_display == 'unlimited':
            data = 'unlimited'
        else:
            data = int(obj.data_limit_gb) if obj.data_limit_gb else 0

        voice_display = obj.voice_limit_display_qty
        if voice_display == 'unlimited':
            calls = 'unlimited'
        else:
            calls = int(obj.voice_limit) if obj.voice_limit else 0

        sms_display = obj.sms_limit_display_qty
        if sms_display == 'unlimited':
            texts = 'unlimited'
        else:
            texts = int(obj.sms_limit) if obj.sms_limit else 0

        return {
            "data": data,
            "calls": calls,
            "texts": texts,
            "europe_data": obj.base_eu_roaming_days
        }

    def get_travel_addons(self, obj):
        plan_travel_addons = PlanTravelAddon.objects.filter(
            plan=obj,
            is_active=True,
            travel_addon__is_active=True
        ).select_related('travel_addon').prefetch_related('travel_addon__roaming_esim_packages')

        return TravelAddonSerializer(plan_travel_addons, many=True).data

    def get_family_plans(self, obj):
        family_links = FamilyPlanLink.objects.filter(
            main_plan=obj,
            is_active=True
        ).select_related('sub_plan')

        return FamilyPlanSerializer(family_links, many=True).data

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'name': instance.name,
            'allowances': self.get_allowances(instance),
            'travel_addons': self.get_travel_addons(instance),
            'price': float(instance.price),
            'family_plans': self.get_family_plans(instance)
        }

class SimActivationSerializer(serializers.ModelSerializer):
    sim_type = serializers.CharField(read_only=True)
    esim_code = serializers.SerializerMethodField()
    esim_address = serializers.SerializerMethodField()

    class Meta:
        model = Sim
        fields = ['serial_number', 'status', 'sim_type', 'esim_data', 'esim_code', 'esim_address']

    def get_esim_code(self, obj):
        if obj.is_esim and obj.esim_data:
            try:
                return obj.esim_code
            except (AttributeError, ValueError, IndexError):
                return None
        return None

    def get_esim_address(self, obj):
        if obj.is_esim and obj.esim_data:
            try:
                return obj.esim_address
            except (AttributeError, ValueError, IndexError):
                return None
        return None

class NextApiSimSerializer(DataApiSimSerializer):
    correlation_id = serializers.SerializerMethodField()
    token = serializers.CharField(max_length=1000, default='sampletoken')

    class Meta(DataApiSimSerializer.Meta):
        fields = DataApiSimSerializer.Meta.fields + ['correlation_id', 'token']

    def get_correlation_id(self, obj):
        if obj.latest_subscription:
            if obj.latest_subscription.correlation_id:
                return obj.latest_subscription.correlation_id
        return ''

class NextApiSubscriptionSerializer(DataApiSubscriptionSerializer):
    correlation_id = serializers.SerializerMethodField()
    usage = serializers.SerializerMethodField()
    roaming_usage = serializers.SerializerMethodField()
    current_migration = serializers.SerializerMethodField()

    class Meta(DataApiSubscriptionSerializer.Meta):
        fields = DataApiSubscriptionSerializer.Meta.fields + ['correlation_id', 'usage', 'roaming_usage', 'current_migration']

    def get_correlation_id(self, obj):
        if obj.correlation_id:
            return obj.correlation_id
        return ''

    def _get_component_usage(self, sim, msisdn, plan, component):
        try:
            usage = getattr(sim, f'get_{component}_usage_this_billing_month')(msisdn) or 0
            is_unlimited = getattr(plan, f'{component}_is_unlimited')
            limit = getattr(plan, f'{component}_limit' + ('_bytes' if component == 'data' else ''))
            return {
                "used": usage,
                "remaining": None if is_unlimited else (limit - usage)
            }
        except (AttributeError, TypeError):
            return {
                "used": 0,
                "remaining": None
            }

    def get_usage(self, obj):
        sim = obj.latest_sim
        msisdn = obj.latest_msisdn

        if not sim or not msisdn:
            datetimes = obj.time_control.current_month_datetimes
            return {
                'uk': {
                    'data': {'used': 0, 'remaining': None},
                    'voice': {'used': 0, 'remaining': None},
                    'sms': {'used': 0, 'remaining': None}
                },
                'europe': {
                    'data': {'used': 0, 'remaining': None},
                    'voice': {'used': 0, 'remaining': None},
                    'sms': {'used': 0, 'remaining': None}
                },
                'period_start': datetimes[0].isoformat(),
                'period_end': datetimes[1].isoformat(),
                'billing_cycle_period': 'monthly'
            }

        plan = sim.latest_plan
        if not plan:
            datetimes = obj.time_control.current_month_datetimes
            return {
                'uk': {
                    'data': {'used': 0, 'remaining': None},
                    'voice': {'used': 0, 'remaining': None},
                    'sms': {'used': 0, 'remaining': None}
                },
                'europe': {
                    'data': {'used': 0, 'remaining': None},
                    'voice': {'used': 0, 'remaining': None},
                    'sms': {'used': 0, 'remaining': None}
                },
                'period_start': datetimes[0].isoformat(),
                'period_end': datetimes[1].isoformat(),
                'billing_cycle_period': 'monthly'
            }

        datetimes = obj.time_control.current_month_datetimes

        uk_usage = {
            'data': self._get_component_usage(sim, msisdn, plan, 'data'),
            'voice': self._get_component_usage(sim, msisdn, plan, 'voice'),
            'sms': self._get_component_usage(sim, msisdn, plan, 'sms')
        }

        europe_usage = {
            'data': {'used': 0, 'remaining': None},
            'voice': {'used': 0, 'remaining': None},
            'sms': {'used': 0, 'remaining': None}
        }

        return {
            'uk': uk_usage,
            'europe': europe_usage,
            'period_start': datetimes[0].isoformat(),
            'period_end': datetimes[1].isoformat(),
            'billing_cycle_period': 'monthly'
        }

    def get_roaming_usage(self, obj):
        try:
            days_used = obj.eu_roaming_days_this_month
            days_allowed = obj.total_available_eu_roaming_days

            return {
                'days_used': days_used,
                'days_allowed': days_allowed
            }
        except (AttributeError, TypeError):
            return {
                'days_used': 0,
                'days_allowed': 0
            }

    def get_current_migration(self, obj):
        pc = PlanChange.objects.filter(subscription=obj, change_type=PlanChange.ChangeType.CANCELLATION).not_cancelled().active().first()
        if pc:
            return NextMigrationSerializer(pc).data
        return None


class NextMigrationSerializer(ReadonlySerializer):
    id = serializers.IntegerField()
    target_plan_id = serializers.IntegerField()
    scheduled_date = serializers.SerializerMethodField()
    status = serializers.CharField(source='subscriber_status_display')
    change_type = serializers.CharField()

    def get_scheduled_date(self, obj):
        if obj.change_type == PlanChange.ChangeType.CANCELLATION:
            return STRIPE_BILLING_TIME_CONTROL.start_of_next_cycle.isoformat()
        return None
