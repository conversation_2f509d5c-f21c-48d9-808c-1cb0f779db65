# pylint: disable=cyclic-import
import string
import base64
import random
import secrets
import urllib.parse
import uuid
from contextlib import contextmanager
from datetime import timedelta, datetime
from decimal import Decimal
from email.mime.image import MIMEImage
from functools import lru_cache
from io import BytesIO
from math import ceil
from dateutil.relativedelta import relativedelta
from polymorphic.models import PolymorphicModel
import qrcode
from qrcode.image.pure import PyPNGImage
from django.apps import apps
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import models, transaction
from django.db.models import Q, Min, F, ExpressionWrapper, fields, Count, Sum
from django.db.models.functions import ExtractYear, ExtractMonth
from django.utils import timezone
from core.utils import gb_to_bytes, bytes_to_gb, phone_number_to_msisdn
from .billing import prorate_plan_limit
from .utils import get_stripe_client, ensure_uk_prefix
from .provider_interface import TransatelInterface, GammaInterface, DemoInterface, StagingInterface, TelnaInterface, SandboxInterface
from .telna_client import TelnaClient
from .slack import send_debug_slack_message, send_slack_message


def generate_async_api_token():
    """Generate a 32-character random token for async API authentication."""
    return ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))
class Provider(models.Model):
    name = models.CharField(max_length=255, unique=True)
    unlimited_data_actual_limit = models.IntegerField(default=100, help_text='Actual data limit in gb')
    prorates_on_activation = models.BooleanField(default=True, help_text='Whether the provider prorates on activation')
    only_bills_full_months = models.BooleanField(default=True, help_text='Whether the provider only bills full months')
    is_demo = models.BooleanField(default=False, help_text='Whether this provider is a demo provider')

    @property
    def _provider_interface(self):
        if self.name.lower() == 'transatel':
            return TransatelInterface()
        if self.name.lower() == 'gamma':
            return GammaInterface()
        if self.name.lower() == 'demo':
            return DemoInterface()
        if self.name.lower() == 'staging':
            return StagingInterface()
        if self.name.lower() == 'sandbox':
            return SandboxInterface()
        if self.name.lower() == 'telna':
            return TelnaInterface()
        raise RuntimeError(f'No known provider interface for {self.name}')

    def change_plan(self, sim, new_plan_code, fake=False):
        self._provider_interface.change_plan(sim, new_plan_code, fake=fake)

    def get_plan_code_for_plan(self, plan):
        return self._provider_interface.get_plan_code_for_plan(plan)

    def activate_sim(self, sim, plan_code, reference):
        return self._provider_interface.activate_sim(sim, plan_code, reference)

    def send_pac_request(self, sim, pac_code, msisdn, desired_date=None):
        self._provider_interface.send_pac_request(sim, pac_code, msisdn, desired_date)

    def look_for_pac_out(self, sim_serials):
        self._provider_interface.look_for_pac_out(sim_serials)

    def schedule_unbar(self, iccid):
        time = self.time_control.current_month_datetimes[1].replace(hour=1, minute=0)
        self._provider_interface.unbar_data(iccid, time)

    def cancel_service(self, iccid):
        self._provider_interface.cancel_service(iccid)

    @property
    def time_control(self):
        return self._provider_interface._get_time_control()  # pylint: disable=protected-access

    @property
    def current_billing_month_date_range(self):
        return self.time_control.current_month_datetimes

    def get_usage_this_billing_month(self, sim, msisdn, dimension):
        return self._provider_interface.get_usage_this_billing_month(sim, msisdn, dimension)

    def get_billing_month_date_range_containing(self, start_date):
        return self.time_control.get_month_start_and_end_containing(start_date)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['id']

class Client(models.Model):
    class SubscriberAuthTypeChoices(models.TextChoices):
        COGNITO = 'cognito', 'Cognito'
        OPENID = 'openid', 'OpenID'
    name = models.CharField(max_length=255)
    additional_subscription_discount = models.ForeignKey(
        'PlanDiscount',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="client_additional_discounts"
    )
    spn = models.CharField(max_length=255)
    user_pool_id = models.CharField(max_length=255)
    auth_app_client_id = models.CharField(max_length=255)
    auth_app_client_secret = models.CharField(max_length=255, blank=True, null=True)
    auth_app_domain_prefix = models.CharField(max_length=255)
    auth_app_redirect_uri = models.URLField()
    provider = models.ForeignKey(Provider, on_delete=models.CASCADE, related_name='clients')
    plans_assigned_externally = models.BooleanField(default=False, help_text='Whether plans are assigned externally (e.g. manually or via make). Set to false to have subscription webhooks assign plans. Temporary kludge')
    subscriber_user_pool_id = models.CharField(max_length=255, default='', blank=True, null=False)
    subscriber_user_pool_client_id = models.CharField(max_length=255, default='', blank=True, null=False)
    subscriber_user_pool_client_secret = models.CharField(max_length=255, default='', blank=True, null=False)
    subscriber_user_pool_domain = models.CharField(max_length=255, default='', blank=True, null=False)
    subscriber_webapp_url = models.URLField(blank=True, default='')
    perk_point_name_singular = models.CharField(max_length=50, default='perk point', blank=False, null=False)
    perk_point_name_plural = models.CharField(max_length=50, default='perk points', blank=False, null=False)
    obfuscated_id = models.CharField(max_length=255, blank=True, default='', db_index=True)
    esim_preactive_stock = models.IntegerField(default=0, help_text='The number of eSIMs that are preactivated and ready to be assigned')
    allow_promotion_codes = models.BooleanField(default=False, help_text='Whether promotion codes are allowed for this client on plan signup')
    enable_wallet_checkout = models.BooleanField(default=False, help_text='Whether wallet checkout is enabled for this client')
    sim_fragment = models.CharField(max_length=30, help_text='Left hand fragment for physical sim self-activation', default='', blank=True)
    subscriber_auth_type = models.CharField(max_length=20, choices=SubscriberAuthTypeChoices.choices, default=SubscriberAuthTypeChoices.COGNITO)
    openid_client_id = models.CharField(max_length=255, blank=True, default='')
    openid_client_secret = models.CharField(max_length=255, blank=True, default='')
    openid_issuer_url = models.URLField(blank=True, default='', help_text='The URL of the OpenID Connect issuer (without the /.well-known/openid-configuration, no trailing slash)')
    openid_state_secret = models.CharField(max_length=255, blank=True, default='', help_text='A secret used to generate the state parameter for OpenID Connect and auth the user afterwards')
    openid_scopes = models.CharField(max_length=255, blank=True, default='openid profile email')
    roaming_esim_provider = models.ForeignKey(Provider, on_delete=models.CASCADE, related_name='roaming_esim_clients', blank=True, null=True)
    starting_perk_points = models.IntegerField(default=0, help_text='Allocated on sign up to all accounts, not prorated')
    webhook_signing_key = models.CharField(max_length=255, blank=True, default='', help_text='Secret key used for HMAC signing of outgoing webhooks')

    @property
    def openid_redirect_uri(self):
        return f'{self.subscriber_webapp_url}/openid/callback'

    @property
    def is_openid_subscriber_auth(self):
        return self.subscriber_auth_type == Client.SubscriberAuthTypeChoices.OPENID

    @property
    def is_cognito_subscriber_auth(self):
        return self.subscriber_auth_type == Client.SubscriberAuthTypeChoices.COGNITO

    def trigger_sim_preactivation(self, plan):
        if self.esim_preactive_stock > 0:
            with self.lock():
                count = Sim.objects.esim_available_to(self).preactivating(plan).count()
                count += Sim.objects.esim_available_to(self).preactivated(plan).count()
                if count < self.esim_preactive_stock:
                    sims_to_preactivate = Sim.objects.esim_available_to(self).able_to_preactivate()[:self.esim_preactive_stock - count]
                    for sim in sims_to_preactivate:
                        sim.preactivate(plan)

    @contextmanager
    def lock(self):
        with transaction.atomic():
            client = Client.objects.select_for_update().get(pk=self.pk)
            yield client

    @property
    def is_authenticated(self):
        return True

    @property
    def time_control(self):
        return self.provider.time_control

    @property
    def eu_roaming_bolt_on(self):
        return self.bolt_ons.eu_roaming().first()  # pylint: disable=no-member

    @property
    def client_mobile_name(self):
        try:
            return self.email_config.client_mobile_name  # pylint: disable=no-member
        except EmailConfiguration.DoesNotExist:
            return self.name

    @property
    def has_payment_integration(self):
        try:
            return bool(self.payment_integration)  # pylint: disable=no-member
        except PaymentIntegration.DoesNotExist:
            return False

    @property
    def earliest_plan_assignment_start_date(self):
        return SimPlanAssignment.objects.filter(plan__client=self).aggregate(earliest_start_date=Min('start_date'))['earliest_start_date']

    @property
    def authorisation_url(self):
        quoted_redirect = urllib.parse.quote(self.auth_app_redirect_uri, safe='')
        return f'https://{self.auth_app_domain_prefix}.auth.eu-west-2.amazoncognito.com/authorize?client_id={self.auth_app_client_id}&response_type=token&scope=email+openid+phone&redirect_uri={quoted_redirect}'

    def get_msisdns(self):
        client = Client.objects.get(pk=self.pk)
        sims = Sim.objects.filter(plan_assignments__plan__client=client)
        msisdns = sims.values_list('number_assignments__phone_number', flat=True).distinct()
        return list(msisdns)

    def all_active_subscriptions(self):
        return Subscription.objects.filter(subscriber__client=self, status=Subscription.Statuses.ACTIVE)

    def all_active_primary_subscriptions(self):
        return self.all_active_subscriptions().primary()

    @property
    def bolt_on_purchases(self):
        return BoltOnPurchase.objects.filter(bolt_on__client=self)

    @property
    def _num_bolt_on_purchases(self):
        return self.bolt_on_purchases.count()

    @property
    def _bolt_on_revenue(self):
        return self.bolt_on_purchases.aggregate(Sum('price'))['price__sum'] or 0

    @property
    def _bolt_ons_redeemed_via_perks(self):
        return self.bolt_on_purchases.filter(redeemed_via_perk__isnull=False).count()

    @property
    def _bolt_ons_uptake(self):
        return self.bolt_on_purchases.count() / self.all_active_subscriptions().count()

    @property
    def bolt_on_stats(self):
        return {
            "purchases": self._num_bolt_on_purchases,
            "revenue": self._bolt_on_revenue,
            "perk_redemptions": self._bolt_ons_redeemed_via_perks,
            "uptake": self._bolt_ons_uptake,
        }


    def __str__(self):
        return self.name

    def allocate_points_to_all_subscribers(self, num_points):
        active_primary_subscriptions = self.all_active_primary_subscriptions()
        for subscription in active_primary_subscriptions:
            if subscription.latest_sim:
                subscription.subscriber.allocate_points(num_points, subscription)
    class Meta:
        ordering = ['name']

class PaymentIntegration(models.Model):
    class Gateway(models.TextChoices):
        STRIPE = 'stripe', 'Stripe'
        UNSET = 'unset', 'Unset'

    gateway = models.CharField(max_length=10, choices=Gateway.choices, default=Gateway.UNSET)
    public_credentials = models.TextField(blank=True, default='')
    secret_credentials = models.TextField(blank=True, default='')
    webhook_secret = models.TextField(blank=True, default='')
    client = models.OneToOneField(Client, on_delete=models.CASCADE, related_name='payment_integration')
    is_shared = models.BooleanField(default=False, help_text='Whether this payment integration is shared with other clients. Tick it on all shared payment integrations. This will soften error checking when entities cannot be found and prevent duplication.')

    @property
    def credentals(self):
        return self.secret_credentials

    def __str__(self):
        return f"Payment Integration for {self.client}"


class ClientWebhookConfig(models.Model):
    client = models.OneToOneField(Client, on_delete=models.CASCADE, related_name='webhook_config')
    webhook_url = models.URLField(help_text='URL where webhooks will be sent to the client')
    webhook_secret = models.CharField(max_length=255, help_text='Secret key used for HMAC signing of outgoing webhooks')
    is_enabled = models.BooleanField(default=True, help_text='Whether webhook delivery is enabled for this client')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Webhook Config for {self.client}"

    class Meta:
        verbose_name = 'Client Webhook Configuration'
        verbose_name_plural = 'Client Webhook Configurations'

class DashboardUser(models.Model):
    class RoleChoices(models.TextChoices):
        ADMIN = 'admin', 'Admin'
        MEMBER = 'member', 'Member'

    client = models.ForeignKey(Client, on_delete=models.CASCADE, help_text='Cached, so changes will require a restart to be visible')
    username = models.CharField(max_length=50, help_text='Cognito user pool username, cached', unique=True, db_index=True)
    email = models.EmailField(blank=True, null=True)
    notes = models.TextField(blank=True, default='')
    created = models.DateTimeField(auto_now_add=True)
    role = models.CharField(max_length=20, choices=RoleChoices.choices, default=RoleChoices.MEMBER)

    def __str__(self):
        return self.username

class Font(models.Model):
    name = models.CharField(max_length=50, unique=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']

class ClientBranding(models.Model):
    client = models.OneToOneField('core.Client', on_delete=models.CASCADE, related_name='branding')
    logo = models.TextField()
    headings_font = models.ForeignKey(Font, on_delete=models.CASCADE, related_name='headings_clients')
    paragraph_font = models.ForeignKey(Font, on_delete=models.CASCADE, related_name='paragraph_clients')
    primary_color = models.CharField(max_length=20)
    secondary_color = models.CharField(max_length=20)
    accent_color = models.CharField(max_length=20)
    text_color = models.CharField(max_length=20)
    link_color = models.CharField(max_length=20)

    def __str__(self):
        return f"Branding for {self.client}"

    class Meta:
        ordering = ['client__id']

class Subscriber(models.Model):  # pylint: disable=too-many-public-methods
    class SimTypes(models.TextChoices):
        PHYSICAL = 'physical', 'Physical'
        ESIM = 'esim', 'eSIM'

    email = models.EmailField()
    date_of_birth = models.DateField(blank=True, null=True)
    join_date = models.DateTimeField(auto_now_add=True, help_text='The date when the subscriber joined')
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='subscribers')
    name = models.CharField(max_length=100)
    billing_subscriber_id = models.CharField(max_length=100, blank=True, default='')
    cognito_username = models.CharField(max_length=100, blank=True, default='')
    address = models.TextField(blank=True, default='')
    verification_code = models.CharField(max_length=4, blank=True, default='')
    verify_attempts = models.IntegerField(default=0)
    forgot_password_code = models.CharField(max_length=4, blank=True, default='')
    forgot_password_attempts = models.IntegerField(default=0)
    total_points_earned = models.IntegerField(default=0)
    perk_points = models.IntegerField(default=0)
    intended_sim_type = models.CharField(max_length=10, null=True, blank=True, choices=SimTypes.choices, help_text='The type of SIM the subscriber intends to use (stored for the signup flow) - this belongs on the subscription for multi-subscription but for now it can live here')
    send_marketing = models.BooleanField(default=False)
    referral_credit = models.DecimalField(
        default=0.00,
        max_digits=10,
        decimal_places=2,
        help_text='The referral credit balance for this subscriber.',
    )
    referral_code = models.CharField(max_length=20, blank=True, default='')
    payment_method_reference = models.CharField(max_length=100, blank=True, default='')
    async_api_token = models.CharField(max_length=32, default=generate_async_api_token, unique=True, help_text='32-character token for async API authentication')
    client_reference_id = models.CharField(max_length=100, blank=True, default='', help_text="New API - Client reference ID for the subscriber")
    metadata = models.JSONField(default=dict, blank=True, help_text='Partner-specific metadata stored as JSON (Deliveroo rider information)')


    def allocate_points(self, points, subscription):
        Notification = apps.get_model('simp', 'Notification')
        with self.lock() as subscriber:
            PerkPointAllocation.objects.create(subscription=subscription, amount=points)
            subscriber.perk_points += int(points)
            subscriber.total_points_earned += int(points)
            subscriber.save()
            if settings.SEND_SIMP_NOTIFICATIONS and points > 0:
                Notification.objects.create(
                    subscriber=subscriber,
                    text=f'You earned {points} {subscriber.client.perk_point_name_plural}!'
                )
    @contextmanager
    def lock(self):
        with transaction.atomic():
            subscriber = Subscriber.objects.select_for_update().get(pk=self.pk)
            yield subscriber

    def get_roaming_esims_snapshot(self):
        for roaming_esim_subscription in self.subscriptions.filter(subscription_type=Subscription.SubscriptionTypes.ROAMING_ESIM).order_by('id'):
            snapshot = RoamingEsimSnapshot.from_subscription(roaming_esim_subscription)
            if snapshot:
                yield snapshot
            else:
                send_debug_slack_message(f'No roaming esim snapshot for {roaming_esim_subscription} - {self}')

    @property
    def referral_applied(self):
        return Referral.objects.filter(referred_subscriber=self).exists()

    def generate_referral_code_if_necessary(self):
        if not self.referral_code:
            random_digits = random.randint(1000, 9999)
            self.referral_code = f'Simp{random_digits}{self.pk}'
            self.save()

    def opt_out_of_marketing(self):
        self.send_marketing = False
        self.save()

    def opt_in_to_marketing(self):
        self.send_marketing = True
        self.save()

    @property
    def num_perk_redemptions(self):
        return self.perk_redemptions.count()

    def claim_perk_via_points(self, perk):
        send_debug_slack_message(f'Claiming perk {perk} via points for {self} on {self.client}')
        return PerkRedemption.claim_via_points(self, perk)

    def claim_perk_via_eligibility(self, perk):
        send_debug_slack_message(f'Claiming perk {perk} via eligibility for {self} on {self.client}')
        return PerkRedemption.claim_via_eligibility(self, perk)

    def progress_towards_perk(self, perk):
        if perk.eligibility_type == Perk.EligibilityType.TENURE:
            basis_value = self.tenure_value
        elif perk.eligibility_type == Perk.EligibilityType.TOTAL_SPEND:
            basis_value = self.total_spend
        elif perk.eligibility_type == Perk.EligibilityType.TOTAL_POINTS_EARNED:
            basis_value = self.total_points_earned
        elif perk.eligibility_type == Perk.EligibilityType.NO_FREE:
            return min(100, (self.perk_points / perk.points_cost) * 100)
        elif perk.eligibility_type == Perk.EligibilityType.AIRDROP:
            return 100
        if not basis_value:
            return 0
        if perk.eligibility_threshold == 0:
            return 100
        return min(100, (basis_value / perk.eligibility_threshold) * 100)

    def has_claimed_perk(self, perk):
        return self.perk_redemptions.filter(perk=perk).exists()

    def has_confirmed_perk(self, perk):
        return self.perk_redemptions.filter(perk=perk, is_confirmed=True).exists()

    def can_afford_perk(self, perk):
        return self.perk_points >= perk.points_cost

    def deduct_points(self, amount):
        # Assumed to be taken under lock
        self.perk_points -= amount
        self.save()

    @property
    def total_spend(self):
        return self.revenue_generated

    @property
    def tenure_value(self):
        time_control = self.client.provider.time_control
        return time_control.get_complete_months(self.join_date, timezone.now())

    @property
    def is_verified(self):
        return self.verification_code == ''

    @property
    def group(self):
        return 'Default'

    @property
    def revenue_generated(self):
        return self.subscriptions.aggregate(models.Sum('payments__amount'))['payments__amount__sum'] or 0

    @property
    def status(self):
        return 'Active' if self.subscriptions.filter(status=Subscription.Statuses.ACTIVE).exists() else 'Inactive'

    def __str__(self):
        return str(self.email)

    def record_new_referral(self, new_subscriber, credit_amount):
        with self.lock() as subscriber:
            Referral.objects.create(
                referrer=subscriber,
                referred_subscriber=new_subscriber,
                referral_code=subscriber.referral_code,
                credit_applied=Decimal(credit_amount)
            )
        subscriber.referral_credit += Decimal(credit_amount)
        subscriber.save()

    class Meta:
        ordering = ['-join_date']
        unique_together = [['id', 'billing_subscriber_id']]

class Referral(models.Model):
    referral_code = models.CharField(max_length=20)
    referrer = models.ForeignKey(Subscriber, on_delete=models.CASCADE, related_name='referrals_given')
    referred_subscriber = models.ForeignKey(Subscriber, on_delete=models.CASCADE, related_name='referrals_received')
    referral_date = models.DateTimeField(auto_now_add=True)
    credit_applied = models.DecimalField(max_digits=10, decimal_places=2)

    def __str__(self):
        return f"Referral from {self.referrer} to {self.referred_subscriber}"

class PerkPointAllocationQuerySet(models.QuerySet):
    def date_filter(self, start_date, end_date):
        return self.filter(date__gte=start_date, date__lt=end_date)


class PerkPointAllocation(models.Model):
    subscription = models.ForeignKey('Subscription', on_delete=models.CASCADE, related_name='perk_point_allocations')
    amount = models.IntegerField()
    date = models.DateTimeField(auto_now_add=True)

    objects = models.Manager.from_queryset(PerkPointAllocationQuerySet)()


class SubscriptionQuerySet(models.QuerySet):
    def active(self):
        return self.filter(status=Subscription.Statuses.ACTIVE)

    def active_since(self, date):
        return self.active().filter(start_date__lte=date)

    def active_for(self, months):
        return self.active().annotate(
            year_diff=ExtractYear(F('end_date')) - ExtractYear(F('start_date')),
            month_diff=ExtractMonth(F('end_date')) - ExtractMonth(F('start_date')),
            total_month_diff=ExpressionWrapper(
                (ExtractYear(F('end_date')) - ExtractYear(F('start_date'))) * 12 +
                (ExtractMonth(F('end_date')) - ExtractMonth(F('start_date'))),
                output_field=fields.IntegerField()
            )
        ).filter(total_month_diff__gt=months)

    def primary(self):
        return self.filter(subscription_type=Subscription.SubscriptionTypes.PRIMARY)


class Subscription(models.Model):  # pylint: disable=too-many-public-methods
    class Statuses(models.TextChoices):
        ACTIVE = 'active', 'Active'
        INACTIVE = 'inactive', 'Inactive'
        CANCELLED = 'cancelled', 'Cancelled'

    class SubscriptionTypes(models.TextChoices):
        PRIMARY = 'primary', 'Primary'
        ROAMING_ESIM = 'roaming_esim', 'Roaming eSIM'

    start_date = models.DateTimeField()
    end_date = models.DateTimeField(blank=True, null=True)
    subscriber = models.ForeignKey(Subscriber, on_delete=models.CASCADE, related_name='subscriptions')
    sims = models.ManyToManyField('Sim', through='SimSubscriptionAssignment', related_name='subscriptions')
    status = models.CharField(max_length=20, choices=Statuses.choices, default=Statuses.INACTIVE)
    billing_subscription_id = models.CharField(max_length=100, blank=True, default='')
    cancellation_date = models.DateTimeField(blank=True, null=True)
    intended_plan = models.ForeignKey('Plan', on_delete=models.CASCADE, related_name='subscriptions', blank=True, null=True)
    correlation_id = models.CharField(max_length=100, null=True, blank=True)
    esim_recipient = models.EmailField(blank=True, default='')
    show_number_transfer = models.BooleanField(default=True)
    show_number_porting_progress = models.BooleanField(default=True)
    show_update_apn = models.BooleanField(default=True)
    show_set_up_esim = models.BooleanField(default=True)
    user_subscription_name = models.CharField(max_length=20, default='', blank=True)
    subscription_type = models.CharField(max_length=20, choices=SubscriptionTypes.choices, default=SubscriptionTypes.PRIMARY)

    objects = models.Manager.from_queryset(SubscriptionQuerySet)()

    @property
    def is_active(self):
        return self.status == Subscription.Statuses.ACTIVE

    @property
    def has_paid(self):
        if self.subscription_type == Subscription.SubscriptionTypes.PRIMARY:
            return self.payments.filter(amount__gt=0).exists()
        else:
            primary_subs = self.subscriber.subscriptions.primary()  # pylint: disable=no-member
            for primary in primary_subs:
                if primary.has_paid:
                    return True
        return False

    def try_activating_sim_by_digits(self, sim_digits):
        if not self.has_paid:
            send_slack_message(f'Blocked assigning activating subscription for {self} - maybe hax?')
            return False
        sim = Sim.objects.filter(serial_number=f'{self.client.sim_fragment}{sim_digits}', dispatched_by=self.subscriber.client).first()
        if sim and sim.can_be_self_activated:
            sim.self_activated = True
            sim.save()
            self.activate_physical_sim(sim)
            return True
        return False


    def update_eu_roaming_days(self, days):
        cycle_date = self.current_billing_cycle_start
        with self.subscriber.lock():
            EuRoamingTracking.objects.update_or_create(subscription=self, year=cycle_date.year, month=cycle_date.month, defaults={'roaming_days': days})

    @property
    def eu_roaming_days_this_month(self):
        cycle_date = self.current_billing_cycle_start
        tracking = self.eu_roaming_tracking.filter(year=cycle_date.year, month=cycle_date.month).first()
        if tracking:
            return tracking.roaming_days
        else:
            return 0

    @property
    def total_available_eu_roaming_days(self):
        if self.latest_plan:
            base_days = self.latest_plan.base_eu_roaming_days
        else:
            base_days = 7
        purchases = self.bolt_on_purchases.eu_roaming().active()  # pylint: disable=no-member
        for purchase in purchases:
            base_days += purchase.quantity
        return base_days

    def unpause_collection_if_paused(self):
        if not self.client.plans_assigned_externally and self.billing_subscription_id and not self.billing_subscription_id.startswith('roaming'):
            sim = self.latest_sim
            if sim:
                sim.send_pac_request_if_necessary()
                if self.client.payment_integration: # pylint: disable=no-member
                    if self.client.payment_integration.gateway == PaymentIntegration.Gateway.STRIPE: # pylint: disable=no-member
                        stripe_client = get_stripe_client(
                            self.client.payment_integration.secret_credentials # pylint: disable=no-member
                        )
                        stripe_client.subscriptions.update(self.billing_subscription_id, params={'pause_collection': ''})
                    else:
                        raise RuntimeError('Unknown gateway')

    def allocate_prorated_perk_points(self):
        add_points = False
        with self.subscriber.lock() as subscriber:
            if self.latest_plan:
                base_points = self.latest_plan.points_per_month
            else:
                base_points = self.intended_plan.points_per_month if self.intended_plan else 0
            if base_points > 0:
                start_date, end_date = self.current_billing_cycle_start, self.current_billing_cycle_end
                if PerkPointAllocation.objects.filter(subscription=self).date_filter(start_date, end_date).exists():
                    return
                if base_points > 0:
                    basis_start_date = max(self.start_date, start_date)
                    basis_end_date = end_date
                    if self.end_date:
                        basis_end_date = min(self.end_date, end_date)
                    prorated_points = subscriber.client.provider.time_control.period_multiple(basis_start_date, basis_end_date) * base_points
                    PerkPointAllocation.objects.create(subscription=self, amount=prorated_points)
                    add_points = True
                if add_points:
                    subscriber.perk_points += int(prorated_points)
                    subscriber.save()

    def next_bill_amount(self):
        if self.latest_plan:
            base_price = self.latest_plan.price
        elif self.intended_plan:
            base_price = self.intended_plan.price
        else:
            return 0
        flat_discounts = self.discount_applications.active().flat()  # pylint: disable=no-member
        for discount in flat_discounts:
            base_price -= discount.discount_amount
        percentage_discounts = self.discount_applications.active().percentage()  # pylint: disable=no-member
        for discount in percentage_discounts:
            base_price *= discount.subtracting_multiplier
        return base_price

    def maybe_activate_esim(self, definitely_paid=False):
        if settings.SKIP_NON_PAY_CHECK or self.has_paid or definitely_paid:
            if self.subscriber.intended_sim_type == Subscriber.SimTypes.ESIM and not self.latest_sim:
                esim_base_qs = Sim.objects.esim_available_to(self.subscriber.client).available_for_new_subscription(self.intended_plan)
                esim = esim_base_qs.preactivated(self.intended_plan).first()
                if not esim:
                    esim = esim_base_qs.first()
                now = timezone.now()
                if esim:
                    SimSubscriptionAssignment.objects.create(sim=esim, subscription=self, start_date=now)
                    if not esim.is_preactive:
                        SimPlanAssignment.objects.create(sim=esim, plan=self.intended_plan, start_date=now)
                    esim.activate()
                else:
                    send_slack_message(f'Could not activate esim for {self} as no esim was available')
        else:
            send_slack_message(f'Blocked assigning unpaid subscription for {self} - maybe hax?')


    def try_activate_new_roaming_esim(self, sim_batch, plan_code):
        provider_reference = None
        if not self.latest_sim:
            esim_base_qs = Sim.objects.roaming_esim_available_to(self.subscriber.client).available_for_roaming_subscription(sim_batch)
            esim = esim_base_qs.first()
            now = timezone.now()
            if esim:
                SimSubscriptionAssignment.objects.create(sim=esim, subscription=self, start_date=now)
                provider_reference = esim.activate(plan_code)
            else:
                send_slack_message(f'Could not activate esim for {self} as no esim was available')
        return provider_reference

    def try_extend_roaming_esim(self, plan_code):
        if self.latest_sim:
            if self.subscriber.client.roaming_esim_provider.name.lower() == 'telna':
                return TelnaClient().create_package(self.latest_sim.serial_number, plan_code)
        return None

    def activate_physical_sim(self, sim):
        now = timezone.now()
        if sim and not sim.latest_subscription:
            SimSubscriptionAssignment.objects.create(sim=sim, subscription=self, start_date=now)
            SimPlanAssignment.objects.create(sim=sim, plan=self.intended_plan, start_date=now)
            sim.activate()

    @property
    def plan_change(self):
        return self.plan_changes.filter(status=PlanChange.Status.LOCKED).first()

    @property
    def current_billing_cycle_start(self):
        current_provider = self.latest_provider
        if self.latest_sim and self.latest_sim_assignment:
            return max(self.latest_sim_assignment.start_date, current_provider.current_billing_month_date_range[0])
        else:
            return max(self.start_date, current_provider.current_billing_month_date_range[0])

    @property
    def next_billing_cycle_start(self):
        current_provider = self.latest_provider
        return current_provider.get_billing_month_date_range_containing(self.current_billing_cycle_end + timedelta(days=2))[0]

    @property
    def current_billing_cycle_end(self):
        current_provider = self.latest_provider
        return current_provider.current_billing_month_date_range[1]

    @property
    def status_display_for_frontend(self):
        has_active_plan = getattr(self, '_has_active_plan', False) or self.latest_sim.plan_assignments.filter(end_date__isnull=True).exists() if self.latest_sim else False
        if has_active_plan:
            if self.status == Subscription.Statuses.ACTIVE and (getattr(self, '_latest_sim_active', None) or self.latest_sim.is_active):
                return 'Active'
            elif self.status == Subscription.Statuses.CANCELLED:
                return 'Inactive'
            else:
                return 'Pending'
        else:
            if not self.latest_sim and self.subscriber.intended_sim_type == Subscriber.SimTypes.PHYSICAL and self.has_paid:
                return 'Needs sim'
            if self.status == Subscription.Statuses.CANCELLED:
                return 'Inactive'
            else:
                return 'Pending'

    @property
    def service_type(self):
        if self.subscription_type == self.SubscriptionTypes.ROAMING_ESIM:
            return 'travel'
        return 'local'

    @property
    def client(self):
        return self.subscriber.client

    @property
    def revenue_generated(self):
        if getattr(self, '_total_revenue', None) is not None:
            return self._total_revenue  # pylint: disable=no-member
        return self.payments.aggregate(models.Sum('amount'))['amount__sum'] or 0

    @property
    def latest_provider(self):
        sim = self.latest_sim
        if sim:
            return sim.latest_provider
        return self.subscriber.client.provider

    @property
    def latest_sim(self):
        assignment = self.latest_sim_assignment
        if assignment:
            return assignment.sim
        return None

    @property
    def latest_sim_assignment(self):
        return self.sim_assignments.order_by('-start_date', '-id').first()

    @property
    def latest_number(self):
        assignment = self.sim_assignments.order_by('-start_date').first()
        if assignment:
            return assignment.sim.latest_number
        return None

    @property
    def uk_prefixed_phone_number(self):
        number = self.latest_msisdn
        if number:
            return ensure_uk_prefix(number)
        return None

    @property
    def latest_msisdn(self):
        if getattr(self, '_latest_msisdn', None) is not None:
            return self._latest_msisdn  # pylint: disable=no-member
        latest_number = self.latest_number
        if latest_number:
            return latest_number.phone_number
        return None

    @property
    def latest_plan(self):
        if getattr(self, '_latest_plan', None) is not None:
            return self._latest_plan  # pylint: disable=no-member
        assignment = self.sim_assignments.order_by('-start_date').first()
        if assignment:
            return assignment.sim.latest_plan
        return None

    @property
    def time_control(self):
        return self.subscriber.client.provider.time_control

    @property
    def pac_status(self):
        req = self.pac_requests.order_by('-id').first()
        if req:
            sim = self.latest_sim
            if sim:
                pe = sim.portability_events.order_by('-id').first()
                if pe:
                    return pe.simple_status
            return 'pending'
        return 'not-set'

    @property
    def pac_code_number(self):
        req = self.pac_requests.order_by('-id').first()
        if req:
            return req.phone_number
        return None

    @property
    def pac_code_expected_date_epoch(self):
        if expected_date := self.porting_expected_date:
            return datetime.combine(expected_date, datetime.min.time()).timestamp()
        return None

    @property
    def porting_expected_date(self):
        req = self.pac_requests.order_by('-id').first()
        if req:
            return req.expected_date
        return None

    @property
    def porting_phone_number(self):
        req = self.pac_requests.order_by('-id').first()
        if req:
            return req.phone_number
        return ''

    @property
    def can_upgrade(self):
        if not self.latest_sim:
            return False
        if not self.latest_msisdn:
            return False
        if self.is_too_close_to_billing_cycle_to_change:
            return False
        if self.has_already_changed_plan_today:
            return False
        return not self.active_plan_changes

    @property
    def has_already_changed_plan_today(self):
        return self.plan_changes.active_or_completed().filter(execution_start_time__date=timezone.now().date()).exists()  # pylint: disable=no-member

    @property
    def can_downgrade(self):
        if not self.latest_sim:
            return False
        if not self.latest_msisdn:
            return False
        if self.is_too_close_to_billing_cycle_to_change:
            return False
        return not self.active_plan_changes

    @property
    def can_cancel(self):
        if self.is_too_close_to_billing_cycle_to_change:
            return False
        return self.status == Subscription.Statuses.ACTIVE and not self.active_plan_changes

    @property
    def can_cancel_change(self):
        if self.is_too_close_to_billing_cycle_to_change:
            return False
        return len(self.active_plan_changes) == 1 and self.active_plan_changes[0].can_be_cancelled

    @property
    def active_plan_changes(self):
        return self.plan_changes.active()  # pylint: disable=no-member

    @property
    def is_too_close_to_billing_cycle_to_change(self):
        return timezone.now() > self.billing_cycle_freeze_date

    @property
    def billing_cycle_freeze_date(self):
        cycle_end_date = self.current_billing_cycle_end
        return cycle_end_date - timedelta(hours=settings.BILLING_CYCLE_FREEZE_HOURS)

    def __str__(self):
        return f'({self.pk}) {self.subscriber} subscription'

    class Meta:
        ordering = ['-start_date']
        unique_together = [['subscriber', 'billing_subscription_id']]


class EuRoamingTracking(models.Model):
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE, related_name='eu_roaming_tracking')
    year = models.IntegerField()
    month = models.IntegerField()
    roaming_days = models.IntegerField(default=0)


class SubscriptionPayment(models.Model):
    class Currency(models.TextChoices):
        GBX = 'GBX', 'GBX'
        GBP = 'GBP', 'GBP'
        USD = 'USD', 'USD'
        EUR = 'EUR', 'EUR'

    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE, related_name='payments')
    billing_invoice_id = models.CharField(max_length=100, blank=True, default='', db_index=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, choices=Currency.choices, default=Currency.GBP)
    date = models.DateField()

    class Meta:
        ordering = ['-date']
        unique_together = [['subscription', 'billing_invoice_id']]


class PlanComponent(models.Model):
    class Dimension(models.TextChoices):
        SMS = 'sms', 'SMS'
        VOICE = 'voice', 'Voice'
        DATA = 'data', 'Data'

    provider = models.ForeignKey(Provider, on_delete=models.CASCADE)
    description = models.CharField(max_length=255, blank=True, default='')
    default_price = models.DecimalField(max_digits=10, decimal_places=2)
    default_cost = models.DecimalField(max_digits=10, decimal_places=2)
    dimension = models.CharField(max_length=5, choices=Dimension.choices, db_index=True)
    bundle_only = models.BooleanField(default=False, help_text='Tick if this component is only available in bundles. Untick if you want it to be allowed separately')
    allow_custom_limit = models.BooleanField(default=False, help_text='Allow a limit lower than max limit')
    max_limit = models.IntegerField(default=0, help_text='0 for unlimited')

    @property
    def is_unlimited(self):
        return self.max_limit == 0

    @property
    def dimension_display(self):
        return self.get_dimension_display()

    @property
    def limit_display(self):
        qty = 'unlimited' if self.max_limit == 0 else self.max_limit
        return f'{qty} {self.unit}'

    @property
    def unit(self):
        return {
            'sms': 'texts',
            'voice': 'minutes',
            'data': 'GiB'
        }[self.dimension]

    def __str__(self):
        return f"{self.provider} - {self.description} - {self.dimension_display} - {self.limit_display}"

    class Meta:
        ordering = ['-provider']


class PlanComponentOffering(models.Model):
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    plan_component = models.ForeignKey(PlanComponent, on_delete=models.CASCADE)
    cost_override = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    price_override = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    available_for_new_plans = models.BooleanField(default=True, help_text='Whether the client can use this component in new plans')

    @property
    def cost_to_client(self):
        return self.price

    def is_valid_custom_limit(self, custom_limit):
        return self.allow_custom_limit and (self.max_limit == 0 or (self.max_limit is None or self.max_limit >= custom_limit))

    @property
    def allow_custom_limit(self):
        return self.plan_component.allow_custom_limit

    @property
    def max_limit(self):
        return self.plan_component.max_limit

    @property
    def provider(self):
        return self.plan_component.provider

    @property
    def price(self):
        return self.price_override or self.plan_component.default_price

    def __str__(self):
        return f'{self.plan_component} for {self.client}'

    class Meta:
        unique_together = ['client', 'plan_component']
        ordering = ['-client', '-plan_component']


class BundledPlanOffering(models.Model):
    def clean(self):
        """
        Validate that the selected PlanComponentOffering belongs to the same client as BundledPlanOffering.
        """
        if self.client != self.voice.client:
            raise ValidationError('Voice component must belong to the same client')
        if self.client != self.data.client:
            raise ValidationError('Data component must belong to the same client')
        if self.client != self.sms.client:
            raise ValidationError('Sms component must belong to the same client')

    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    voice = models.ForeignKey(PlanComponentOffering, on_delete=models.CASCADE, limit_choices_to={'plan_component__dimension': 'voice'}, related_name='in_bundles_as_voice')
    sms = models.ForeignKey(PlanComponentOffering, on_delete=models.CASCADE, limit_choices_to={'plan_component__dimension': 'sms'}, related_name='in_bundles_as_sms')
    data = models.ForeignKey(PlanComponentOffering, on_delete=models.CASCADE, limit_choices_to={'plan_component__dimension': 'data'}, related_name='in_bundles_as_data')
    cost_override = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    price_override = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    is_available = models.BooleanField(default=True, help_text='Whether this bundle is available to be selected')
    description = models.CharField(max_length=255, blank=True, default='')

    @property
    def cost_to_client(self):
        return self.price

    @property
    def price(self):
        return self.price_override or (self.sms.price + self.voice.price + self.data.price)

    def __str__(self):
        return f'{self.description} for {self.client}'

    class Meta:
        ordering = ['-client']


class Plan(models.Model):  # pylint: disable=too-many-public-methods

    def clean(self):
        # pylint: disable=too-many-branches
        """
        Validate that the selected PlanComponentOffering belongs to the same client as the Plan.
        Also that the target is on the same client and providers.
        """
        if self.client != self.voice_component_offering.client:
            raise ValidationError('Voice component must belong to the same client as the Plan.')
        if self.client != self.data_component_offering.client:
            raise ValidationError('Data component must belong to the same client as the Plan.')
        if self.client != self.sms_component_offering.client:
            raise ValidationError('Sms component must belong to the same client as the Plan.')
        if self.provider != self.voice_component_offering.provider:
            raise ValidationError('Voice component must belong to the same provider as the Plan.')
        if self.provider != self.data_component_offering.provider:
            raise ValidationError('Data component must belong to the same provider as the Plan.')
        if self.provider != self.sms_component_offering.provider:
            raise ValidationError('Sms component must belong to the same provider as the Plan.')
        if self.upgrade_target:
            if self.upgrade_target.client != self.client:
                raise ValidationError('Upgrade target must belong to the same client as the Plan.')
            if self.upgrade_target.data_component_offering.provider != self.provider:
                raise ValidationError('Upgrade target must belong to the same provider as the Plan.')
        if self.custom_data_limit and self.custom_data_limit > 0:
            if not self.data_component_offering.is_valid_custom_limit(self.custom_data_limit):
                raise ValidationError('Data limit is invalid vs the component (either over max limit or not allowed custom limit')
        if self.custom_sms_limit and self.custom_sms_limit > 0:
            if not self.sms_component_offering.is_valid_custom_limit(self.custom_sms_limit):
                raise ValidationError('Sms limit is invalid vs the component (either over max limit or not allowed custom limit')
        if self.custom_voice_limit and self.custom_voice_limit > 0:
            if not self.voice_component_offering.is_valid_custom_limit(self.custom_voice_limit):
                raise ValidationError('Voice limit is invalid vs the component (either over max limit or not allowed custom limit')

    class PlanStatuses(models.TextChoices):
        INACTIVE = 'inactive', 'Inactive'
        ACTIVE = 'active', 'Active'

    name = models.CharField(max_length=255)
    client = models.ForeignKey('core.Client', on_delete=models.CASCADE, related_name='plans')
    provider = models.ForeignKey('core.Provider', on_delete=models.CASCADE)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=PlanStatuses.choices, default=PlanStatuses.INACTIVE)
    created = models.DateTimeField(auto_now_add=True)
    implementation_datetime = models.DateTimeField(default=timezone.now)
    cost_override = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    plan_key = models.CharField(max_length=255, help_text='Key for grouping the same plan')
    version_number = models.IntegerField(default=0)
    bg = models.IntegerField(default=0)
    points_per_month = models.IntegerField(default=0)
    base_eu_roaming_days = models.IntegerField(default=7)

    voice_component_offering = models.ForeignKey(
        PlanComponentOffering,
        on_delete=models.CASCADE,
        limit_choices_to={'plan_component__dimension': 'voice'},
        null=True,
        blank=True,
        related_name='in_plan_as_voice'
    )
    custom_voice_limit = models.IntegerField(null=True, blank=True)

    sms_component_offering = models.ForeignKey(
        PlanComponentOffering,
        on_delete=models.CASCADE,
        limit_choices_to={'plan_component__dimension': 'sms'},
        null=True,
        blank=True,
        related_name='in_plan_as_sms'
    )
    custom_sms_limit = models.IntegerField(null=True, blank=True)

    data_component_offering = models.ForeignKey(
        PlanComponentOffering,
        on_delete=models.CASCADE,
        limit_choices_to={'plan_component__dimension': 'data'},
        null=True,
        blank=True,
        related_name='in_plan_as_data'
    )
    custom_data_limit = models.IntegerField(null=True, blank=True)
    is_bundled = models.BooleanField(default=False)
    data_warning_threshold_override_gb = models.FloatField(null=True, blank=True, help_text='Warning threshold override in gb, default 80%')
    data_bar_or_upgrade_threshold_override_gb = models.FloatField(null=True, blank=True, help_text='Bar or upgrade threshold override in gb, default 95%')
    upgrade_target = models.ForeignKey('Plan', null=True, blank=True, help_text='Plan to upgrade to when a threshold is reached', on_delete=models.SET_NULL)
    billing_plan_id = models.CharField(max_length=100, blank=True, default='')

    data_allowance_gb = models.IntegerField(default=0, help_text="New API - Data allowance in gibibytes")
    voice_allowance_minutes = models.IntegerField(default=0, help_text="New API - Voice allowance in minutes")
    sms_allowance = models.IntegerField(default=0, help_text="New API - SMS allowance count")
    eu_data_allowance_gb = models.IntegerField(default=0, help_text="New API - EU roaming data allowance in gibibytes")
    eu_voice_allowance_minutes = models.IntegerField(default=0, help_text="New API - EU roaming voice allowance in minutes")
    eu_sms_allowance = models.IntegerField(default=0, help_text="New API - EU roaming SMS allowance count")
    row_data_allowance_gb = models.IntegerField(default=0, help_text="New API - ROW roaming data allowance in gibibytes")
    row_voice_allowance_minutes = models.IntegerField(default=0, help_text="New API - ROW roaming voice allowance in minutes")
    row_sms_allowance = models.IntegerField(default=0, help_text="New API - ROW roaming SMS allowance count")
    api_bundle_id = models.CharField(max_length=100, blank=True, default='', help_text="New API - Bundle ID for the plan, mostly relevant for travel plans")
    client_reference_id = models.CharField(max_length=100, blank=True, default='', help_text="New API - Client reference ID for the plan, used for billing purposes")

    def set_appropriate_status(self):
        if self.implementation_datetime <= timezone.now():
            correct_status = self.PlanStatuses.ACTIVE
        else:
            correct_status = self.PlanStatuses.INACTIVE
        if self.status != correct_status:
            self.save()

    def get_billing_product_id(self):
        if not self.client.plans_assigned_externally:
            if self.client.has_payment_integration and self.client.payment_integration.gateway == PaymentIntegration.Gateway.STRIPE:  # pylint: disable=no-member
                stripe_client = get_stripe_client(self.client.payment_integration.secret_credentials)  # pylint: disable=no-member
                if self.billing_plan_id:
                    price = stripe_client.prices.retrieve(self.billing_plan_id)
                    return price.product
        return None

    def current_price_for_new_subscribers(self, subscriber):
        base_price = self.price
        flat_discounts, percentage_discounts = self.get_discounts_applicable_to_new_subscribers(subscriber)
        for discount in flat_discounts:
            base_price -= discount.discount_amount
        for discount in percentage_discounts:
            base_price *= discount.subtracting_multiplier

        if base_price <= 0:
            send_debug_slack_message(f'Plan {self} has a price of {base_price} after discounts')
        return max(base_price, 0)

    def subscriber_has_active_primary_subscription(self, subscriber):
        return Subscription.objects.filter(
            subscriber=subscriber,
            subscription_type=Subscription.SubscriptionTypes.PRIMARY,
            status=Subscription.Statuses.ACTIVE
        ).exists()

    def get_discounts_applicable_to_new_subscribers(self, subscriber=None):
        if not subscriber or not subscriber.subscriptions.primary().exists():
            active_campaigns = self.client.campaigns.active().applies_to_new_subscribers()  # pylint: disable=no-member
        else:
            active_campaigns = self.client.campaigns.active().applies_to_existing_subscribers()  # pylint: disable=no-member
        flat_discounts = list(PlanDiscount.objects.for_campaigns(active_campaigns).flat().applicable_to_plan(self))
        percentage_discounts = list(PlanDiscount.objects.for_campaigns(active_campaigns).percentage().applicable_to_plan(self))
        if (
            subscriber
            and self.client.additional_subscription_discount
            and self.subscriber_has_active_primary_subscription(subscriber)
        ):
            if self.client.additional_subscription_discount.discount_type == PlanDiscount.DiscountType.FLAT:
                flat_discounts.append(self.client.additional_subscription_discount)
            elif self.client.additional_subscription_discount.discount_type == PlanDiscount.DiscountType.PERCENTAGE:
                percentage_discounts.append(self.client.additional_subscription_discount)
            else:
                raise RuntimeError(f'Unhandled discount type: {self.client.additional_subscription_discount.discount_type}')
        return flat_discounts, percentage_discounts

    @property
    def is_active(self):
        return self.status == self.PlanStatuses.ACTIVE

    @property
    def client_profit(self):
        return self.price - self.cost_to_client

    @property
    def cost_to_client(self):
        if self.cost_override:
            return self.cost_override
        bundle = self.bundle
        if bundle:
            return bundle.cost_to_client
        return self.voice_component_offering.cost_to_client + self.sms_component_offering.cost_to_client + self.data_component_offering.cost_to_client

    @property
    def data_limit_bytes(self):
        return gb_to_bytes(self.data_limit_gb)

    @property
    def data_component(self):
        return self.data_component_offering.plan_component

    @property
    def data_limit_gb(self):
        limit = None
        if self.custom_data_limit and self.custom_data_limit > 0:
            limit = self.custom_data_limit
        else:
            data_component = self.data_component_offering.plan_component
            unlimited = data_component.provider.unlimited_data_actual_limit
            if self.custom_data_limit == 0:
                limit = unlimited
            else:
                if data_component.is_unlimited:
                    limit = unlimited
                else:
                    limit = data_component.max_limit
        return limit

    @property
    def data_limit_display_qty(self):
        if not self.custom_data_limit:
            if self.custom_data_limit == 0 or self.data_component_offering.plan_component.is_unlimited:
                return 'unlimited'
        return self.data_limit_gb

    @property
    def data_limit_display_qty_suffix(self):
        if not self.custom_data_limit:
            if self.custom_data_limit == 0 or self.data_component_offering.plan_component.is_unlimited:
                return 'unlimited'
        return f'{int(self.data_limit_gb)}GB'

    @property
    def data_is_unlimited(self):
        return self.data_limit_display_qty == 'unlimited'

    @property
    def sms_limit(self):
        limit = None
        if self.custom_sms_limit and self.custom_sms_limit > 0:
            limit = self.custom_sms_limit
        else:
            sms_component = self.sms_component_offering.plan_component
            limit = sms_component.max_limit
        return limit

    @property
    def sms_limit_display_qty(self):
        lim = self.sms_limit
        if lim == 0:
            return 'unlimited'
        return lim

    @property
    def sms_limit_display_qty_suffix(self):
        lim = self.sms_limit
        if lim == 0:
            return 'unlimited'
        return f'{lim} texts'

    @property
    def sms_is_unlimited(self):
        return self.sms_limit == 0

    @property
    def voice_limit(self):
        limit = None
        if self.custom_voice_limit and self.custom_voice_limit > 0:
            limit = self.custom_voice_limit
        else:
            voice_component = self.voice_component_offering.plan_component
            limit = voice_component.max_limit
        return limit

    @property
    def voice_limit_display_qty(self):
        lim = self.voice_limit
        if lim == 0:
            return 'unlimited'
        return lim

    @property
    def voice_limit_display_qty_suffix(self):
        lim = self.voice_limit
        if lim == 0:
            return 'unlimited'
        return f'{lim} minutes'

    @property
    def voice_is_unlimited(self):
        return self.voice_limit == 0

    def data_usage_bytes_percent(self, usage_bytes):
        return (usage_bytes / self.data_limit_bytes) * 100

    def get_data_usage_warning_threshold_bytes(self):
        if self.data_warning_threshold_override_gb:
            return gb_to_bytes(self.data_warning_threshold_override_gb)
        return int(self.data_limit_bytes * 0.8)

    def get_data_bar_or_upgrade_threshold_bytes(self):
        if self.data_bar_or_upgrade_threshold_override_gb:
            return gb_to_bytes(self.data_bar_or_upgrade_threshold_override_gb)
        return self.data_limit_bytes * 0.95

    @property
    def bundle(self):
        if self.is_bundled:
            return BundledPlanOffering.objects.get(data=self.data_component_offering, voice=self.voice_component_offering, sms=self.sms_component_offering)
        return None

    @property
    def bundle_id(self):
        bundle = self.bundle
        if bundle:
            return bundle.pk
        return None

    def sync_to_gateway_if_necessary(self):
        if not self.client.plans_assigned_externally:
            if self.client.has_payment_integration and self.client.payment_integration.gateway == PaymentIntegration.Gateway.STRIPE:  # pylint: disable=no-member
                stripe_client = get_stripe_client(self.client.payment_integration.secret_credentials)  # pylint: disable=no-member
                if self.billing_plan_id:
                    price = stripe_client.prices.retrieve(self.billing_plan_id)
                    stripe_client.products.update(price.product, params={'name': self.name})
                    stripe_client.prices.update(price.id, params={'active': False})
                    price = stripe_client.prices.create(params={'product': price.product, 'unit_amount': int(self.price * 100), 'currency': 'gbp', 'recurring': {'interval': 'month'}})
                    self.billing_plan_id = price.id
                    self.save()
                else:
                    product = stripe_client.products.create(params={'name': self.name})
                    price = stripe_client.prices.create(params={'product': product.id, 'unit_amount': int(self.price * 100), 'currency': 'gbp', 'recurring': {'interval': 'month'}})
                    self.billing_plan_id = price.id
                    self.save()

    def is_upgrade_if_going_to(self, target_plan):
        return target_plan.price >= self.price

    @property
    def provider_plan_code(self):
        return self.client.provider.get_plan_code_for_plan(self)

    def __str__(self):
        return f'{self.name} ({self.id})'

    class Meta:
        ordering = ['name']



class SimBatch(models.Model):
    name = models.CharField(max_length=255)



class SimQuerySet(models.QuerySet):
    def esim_available_to(self, client):
        return self.filter(esim_available_to=client, esim_data__isnull=False)

    def roaming_esim_available_to(self, client):
        return self.filter(roaming_esim_available_to=client, esim_data__isnull=False)

    def able_to_preactivate(self):
        return self.filter(status=Sim.SimStatuses.INACTIVE, esim_data__isnull=False, subscription_assignments__isnull=True, plan_assignments__isnull=True)

    def available_for_roaming_subscription(self, sim_batch=None):
        inactive_or_preactivated = Q(status=Sim.SimStatuses.INACTIVE) | Q(status=Sim.SimStatuses.PREACTIVE)
        return self.filter(inactive_or_preactivated, subscription_assignments__isnull=True, plan_assignments__isnull=True, sim_batch=sim_batch)

    def available_for_new_subscription(self, plan):
        inactive_or_preactivated = Q(status=Sim.SimStatuses.INACTIVE) | Q(status=Sim.SimStatuses.PREACTIVE, plan_assignments__plan=plan)
        return self.filter(inactive_or_preactivated, subscription_assignments__isnull=True)

    def preactivated(self, plan):
        return self.filter(activation_date__isnull=False, status=Sim.SimStatuses.PREACTIVE, subscription_assignments__isnull=True, plan_assignments__plan=plan)

    def preactivatable(self):
        return self.filter(activation_date__isnull=True, status=Sim.SimStatuses.INACTIVE, subscription_assignments__isnull=True, plan_assignments__isnull=True)

    def preactivating(self, plan):
        return self.filter(status=Sim.SimStatuses.PREACTIVATING, plan_assignments__plan=plan)

    def dispatchable_by_serial_and_client(self, serial, client):
        return self.filter(serial_number=serial, dispatched_by=client, status=Sim.SimStatuses.INACTIVE).first()

    def get_by_msisdn(self, msisdn):
        return self.get(number_assignments__phone_number=msisdn)

    def get_by_sim_serial(self, serial):
        return self.get(serial_number=serial)

    def for_client(self, client):
        return self.filter(Q(subscriptions__subscriber__client=client) | Q(plan_assignments__plan__client=client)).distinct()


# pylint: disable=too-many-public-methods
class Sim(models.Model):
    class SimStatuses(models.TextChoices):
        INACTIVE = 'inactive', 'Inactive'
        ACTIVE = 'active', 'Active'
        SUSPENDED = 'suspended', 'Suspended'
        TERMINATED = 'terminated', 'Terminated'
        PENDING_ACTIVATION = 'pending-activation', 'Pending Activation'
        REQUIRES_ACTIVATION = 'requires-activation', 'Requires Activation'
        PREACTIVATING = 'preactivating', 'Preactivating'
        PREACTIVE = 'preactive', 'Preactive'


    objects = models.Manager.from_queryset(SimQuerySet)()
    serial_number = models.CharField(max_length=20)
    plans = models.ManyToManyField(Plan, through='SimPlanAssignment', related_name='sims')
    status = models.CharField(max_length=20, choices=SimStatuses.choices, default=SimStatuses.INACTIVE)
    activation_date = models.DateTimeField(blank=True, null=True, help_text='Date the sim was activated - used for prorating')
    is_data_barred = models.BooleanField(default=False, help_text='Temporary kludge until we can determine barring status from API')
    provider_plan_override = models.CharField(max_length=100, null=True, blank=True)
    esim_data = models.CharField(max_length=300, null=True, blank=True, help_text='The full QR data if esim (LPA:...$...$...ABC), blank for physical')
    esim_available_to = models.ForeignKey(Client, on_delete=models.CASCADE, null=True, blank=True, related_name='esims')
    dispatched_by = models.ForeignKey(Client, on_delete=models.CASCADE, null=True, blank=True, related_name='dispatched_sims', help_text='The client that will (or has) dispatched this sim')
    roaming_esim_available_to = models.ForeignKey(Client, on_delete=models.CASCADE, null=True, blank=True, related_name='roaming_esims')
    self_activated = models.BooleanField(default=False, help_text='Whether or not the sim was self activated by the user.')
    sim_batch = models.ForeignKey(SimBatch, on_delete=models.SET_NULL, null=True, blank=True, related_name='sims', help_text='The batch (e.g. nordic profile type) this sim belongs to')
    esim_installed = models.BooleanField(default=False, help_text='Whether or not the esim was installed by the user.')
    esim_enabled = models.BooleanField(default=False, help_text='Whether or not the esim is enabled by the user.')

    NO_SERIAL = 'no-serial'

    @contextmanager
    def lock(self):
        with transaction.atomic():
            sim = Sim.objects.select_for_update().get(pk=self.pk)
            yield sim

    @property
    def esim_code(self):
        return self.esim_parts['code']

    @property
    def esim_address(self):
        return self.esim_parts['address']

    @property
    def esim_parts(self):
        lpa, address, code = self.esim_data.split('$')
        return {
            'lpa': lpa,
            'address': address,
            'code': code
        }

    def can_be_self_activated(self):
        return self.is_physical_sim and self.status == Sim.SimStatuses.INACTIVE

    def get_lifetime_profit(self):
        basis_start_date = None
        if self.latest_subscription:
            basis_start_date = self.latest_subscription.start_date
        return sum(assignment.get_lifetime_profit(basis_start_date) for assignment in self.plan_assignments.all())

    @property
    def this_month_data_limit_display(self):
        if self.latest_plan_assignment:
            return bytes_to_gb(self.data_limit_bytes_this_month)
        return 0

    @property
    def data_limit_bytes_this_month(self):
        current_provider = self.latest_provider
        is_first_month = self.started_new_plan_this_month
        start_date, end_date = current_provider.current_billing_month_date_range
        if not current_provider.only_bills_full_months:
            send_debug_slack_message(f'For {self} cannot calculate data limit, provider does not only bill full months')
            raise RuntimeError('Provider does not only bill full months, cannot currently calculate data limit safely')
        if self.latest_plan_assignment:
            if is_first_month:
                if current_provider.prorates_on_activation:
                    return prorate_plan_limit(self.latest_plan.data_limit_bytes, start_date, end_date, max(self.activation_date or self.latest_plan_assignment.start_date, self.latest_plan_assignment.start_date))
                else:
                    return self.latest_plan.data_limit_bytes
            else:
                return self.latest_plan.data_limit_bytes
        else:
            send_debug_slack_message(f'For {self} cannot calculate data limit, no assignment')
            raise RuntimeError('Provider prorates on activation but no plan assignment')

    @property
    def started_new_plan_this_month(self):
        if self.latest_plan_assignment:
            start_date, _ = self.latest_provider.current_billing_month_date_range
            return self.latest_plan_assignment.started_after(start_date)
        return False

    @property
    def warning_threshold_bytes_this_month(self):
        current_provider = self.latest_provider
        if current_provider.prorates_on_activation and current_provider.only_bills_full_months and self.started_new_plan_this_month:
            return int(self.data_limit_bytes_this_month * 0.8)
        if self.provider_plan_override:
            if self.provider_plan_override == 'TSL_UK_DATA_30GB':
                return gb_to_bytes(27)
            else:
                raise RuntimeError(f'No known provider plan override {self.provider_plan_override}')
        return self.latest_plan.get_data_usage_warning_threshold_bytes()

    @property
    def bar_or_upgrade_threshold_bytes_this_month(self):
        current_provider = self.latest_provider
        if current_provider.prorates_on_activation and current_provider.only_bills_full_months and self.started_new_plan_this_month:
            return int(self.data_limit_bytes_this_month * 0.95)
        if self.provider_plan_override:
            if self.provider_plan_override == 'TSL_UK_DATA_30GB':
                return gb_to_bytes(29)
            else:
                raise RuntimeError(f'No known provider plan override {self.provider_plan_override}')
        return self.latest_plan.get_data_bar_or_upgrade_threshold_bytes()

    def get_data_usage_this_billing_month(self, msisdn):
        return self.client.provider.get_usage_this_billing_month(self, msisdn, 'data')

    def get_voice_usage_this_billing_month(self, msisdn):
        return self.client.provider.get_usage_this_billing_month(self, msisdn, 'voice')

    def get_sms_usage_this_billing_month(self, msisdn):
        return self.client.provider.get_usage_this_billing_month(self, msisdn, 'sms')

    @property
    def data_limit_gb_this_month(self):
        return bytes_to_gb(self.data_limit_bytes_this_month)

    @property
    def latest_provider(self):
        client = self.client
        if client:
            return self.client.provider
        return None

    @property
    def latest_subscription(self):
        return self.subscriptions.order_by('-start_date').first()

    @property
    def latest_subscription_assignment(self):
        return self.subscription_assignments.order_by('-start_date', '-id').first()

    @property
    def latest_subscriber(self):
        subscription = self.latest_subscription
        if subscription:
            return subscription.subscriber
        return None

    @property
    def latest_number(self):
        assignment = self.number_assignments.order_by('-start_date', '-id').first()
        if assignment:
            return assignment
        return None

    @property
    def latest_number_assignment(self):
        return self.latest_number

    @property
    def latest_msisdn(self):
        return self.latest_number.phone_number

    @property
    def latest_plan_assignment(self):
        return self.plan_assignments.order_by('-start_date').first()

    @property
    def latest_plan(self):
        assignment = self.latest_plan_assignment
        if assignment:
            return assignment.plan
        return None

    @property
    def client(self):
        plan = self.latest_plan
        if plan:
            return plan.client
        subs = self.latest_subscriber
        if subs:
            return subs.client
        if self.dispatched_by:
            return self.dispatched_by
        if self.esim_available_to:
            return self.esim_available_to
        if self.roaming_esim_available_to:
            return self.roaming_esim_available_to
        return None

    def __str__(self):
        return f'{self.serial_number}'

    def immediately_move_to_new_plan(self, new_plan, move_date=None):
        move_date = move_date or timezone.now()
        if assignment := self.latest_plan_assignment:
            assignment.end_date = move_date
            assignment.save()
        SimPlanAssignment.objects.create(sim=self, plan=new_plan, start_date=move_date)

    def immediately_move_to_new_msisdn(self, new_msisdn):
        timestamp = timezone.now()
        if assignment := self.latest_number_assignment:
            assignment.end_date = timestamp
            assignment.save()
        NumberAssignment.objects.create(sim=self, phone_number=new_msisdn, start_date=timestamp)

    @property
    def is_active(self):
        return self.status == self.SimStatuses.ACTIVE

    @property
    def is_preactive(self):
        return self.status == self.SimStatuses.PREACTIVE

    @property
    def is_preactivating(self):
        return self.status == self.SimStatuses.PREACTIVATING

    @property
    def is_suspended(self):
        return self.status == self.SimStatuses.SUSPENDED

    @property
    def is_old_sim_in_sim_swap(self):
        return self.is_suspended and self.latest_number_assignment.end_date and self.latest_subscription.latest_sim != self and self.latest_subscription.latest_msisdn == self.latest_msisdn and self.latest_subscription.latest_sim.activation_date == self.activation_date

    def preactivate(self, plan):
        self.status = Sim.SimStatuses.PREACTIVATING
        self.save()
        now = timezone.now()
        SimPlanAssignment.objects.create(sim=self, plan=plan, start_date=now)
        plan_code = plan.client.provider.get_plan_code_for_plan(plan)
        plan.client.provider.activate_sim(self, plan_code, f'c: {plan.client.pk} -- preactivate')

    def activate(self, plan_code=None):
        ref = None
        if self.status == Sim.SimStatuses.PREACTIVE:
            self.status = Sim.SimStatuses.ACTIVE
            subs = self.latest_subscription
            if subs:
                subs.unpause_collection_if_paused()

        else:
            self.status = Sim.SimStatuses.PENDING_ACTIVATION
            if self.is_roaming_esim:
                if plan_code is None:
                    send_debug_slack_message(f'For {self} no plan code provided')
                    raise RuntimeError('No plan code provided for roaming esim')
                ref = self.client.roaming_esim_provider.activate_sim(self, plan_code, f'c: {self.client.pk} -- subscription: {self.latest_subscription.pk}')
            else:
                plan_code = self.client.provider.get_plan_code_for_plan(self.latest_plan)
                ref = self.client.provider.activate_sim(self, plan_code, f'c: {self.client.pk} -- subscription: {self.latest_subscription.pk}')
        self.save()
        self.trigger_potential_preactivation()
        return ref

    def trigger_potential_preactivation(self):
        if self.latest_plan:
            self.latest_plan.client.trigger_sim_preactivation(self.latest_plan)

    def allocate_msisdn(self, msisdn):
        self.immediately_move_to_new_msisdn(msisdn)

    def complete_preactivation(self):
        self.status = Sim.SimStatuses.PREACTIVE
        self.activation_date = timezone.now()
        self.save()

    def complete_activation(self):
        self.status = Sim.SimStatuses.ACTIVE
        self.activation_date = timezone.now()
        self.save()

    def deactivate(self):
        self.status = Sim.SimStatuses.INACTIVE
        self.save()

    def terminate(self):
        self.status = Sim.SimStatuses.TERMINATED
        self.save()

    def trigger_change_plan(self, new_plan_code, fake=False):
        self.client.provider.change_plan(self, new_plan_code, fake=fake)

    def send_pac_request_if_necessary(self):
        subs = self.latest_subscription
        if subs and self.status == Sim.SimStatuses.ACTIVE:
            pac_requests = subs.pac_requests.all()
            if pac_requests.count() > 1:
                raise RuntimeError('Too many pac requests')
            elif pac_requests.count() == 1:
                pac_request = pac_requests.first()
                if not PortabilityEvent.objects.filter(sim=self, msisdn=pac_request.phone_number).exists():
                    self.client.provider.send_pac_request(self, pac_request.pac_code, pac_request.msisdn, pac_request.desired_date)

    @property
    def sim_type(self):
        if self.esim_data:
            if self.roaming_esim_available_to:
                return 'roaming-esim'
            return 'esim'
        return 'physical'

    @property
    def service_type(self):
        if self.roaming_esim_available_to:
            return 'travel'
        return 'local'

    @property
    def is_esim(self):
        return self.sim_type == 'esim'

    @property
    def is_roaming_esim(self):
        return self.sim_type == 'roaming-esim'

    @property
    def is_physical_sim(self):
        return self.sim_type == 'physical'

    @property
    def esim_qr_as_base64(self):
        img = qrcode.make(self.esim_data, image_factory=PyPNGImage)
        buffered = BytesIO()
        img.save(buffered)
        return base64.b64encode(buffered.getvalue()).decode('utf-8')

    @property
    def qr_code_base64(self):
        if self.esim_data:
            return self.esim_qr_as_base64
        return None

    @property
    def qr_code_image(self):
        if self.esim_data:
            return self.esim_qr_as_png
        return None

    @property
    def sm_dp_address(self):
        if self.esim_data:
            return self.esim_parts['address']
        return None

    @property
    def activation_code(self):
        if self.esim_data:
            return self.esim_parts['code']
        return None

    @property
    def ios_universal_link(self):
        if self.esim_data:
            return f'https://esimsetup.apple.com/esim_qrcode_provisioning?carddata={self.esim_data}'
        return None

    @property
    def android_activation_data(self):
        return self.esim_data

    @property
    def esim_qr_as_png(self):
        img = qrcode.make(self.esim_data, image_factory=PyPNGImage)
        buffered = BytesIO()
        img.save(buffered)
        return buffered.getvalue()

    class Meta:
        ordering = ['serial_number']


class SimPlanAssignmentQuerySet(models.QuerySet):
    def date_filter(self, start_date, end_date):
        start_date_before_but_still_active = Q(start_date__lte=end_date) & Q(end_date__isnull=True)
        start_date_in_range = Q(start_date__gte=start_date) & Q(start_date__lt=end_date)
        end_date_in_range = Q(end_date__isnull=False) & Q(end_date__gte=start_date) & Q(end_date__lt=end_date)
        return self.filter(start_date_in_range | end_date_in_range | start_date_before_but_still_active)


class SimPlanAssignment(models.Model):
    objects = models.Manager.from_queryset(SimPlanAssignmentQuerySet)()

    sim = models.ForeignKey(Sim, on_delete=models.CASCADE, related_name='plan_assignments')
    plan = models.ForeignKey(Plan, on_delete=models.CASCADE, related_name='sim_assignments')
    start_date = models.DateTimeField()
    end_date = models.DateTimeField(blank=True, null=True)

    def get_lifetime_profit(self, start_date=None):
        if start_date:
            basis_start_date = max(start_date, self.start_date)
        else:
            basis_start_date = self.start_date
        return self.get_prorated_client_profit(basis_start_date, self.end_date or self.plan.client.provider.current_billing_month_date_range[1])

    @lru_cache
    def get_prorated_client_profit(self, start_date, end_date):
        if self.end_date:
            basis_end_date = min(end_date, self.end_date)
        else:
            basis_end_date = end_date
        basis_start_date = max(start_date, self.start_date)
        provider = self.plan.client.provider
        return provider.time_control.period_multiple(basis_start_date, basis_end_date) * self.plan.client_profit

    def get_duration_in_days(self, start_date, end_date):
        useful_start_date = max(self.start_date, start_date)
        basis_end_date = end_date if self.end_date is None else self.end_date
        useful_end_date = min(basis_end_date, end_date)
        return ceil((useful_end_date - useful_start_date).total_seconds() / 86400.0)

    def started_after(self, date):
        return self.start_date >= date

    def __str__(self):
        return f'{self.plan} assigned to {self.sim}'

    class Meta:
        ordering = ['-start_date']


class SimSubscriptionAssignmentQuerySet(models.QuerySet):
    def date_filter(self, start_date, end_date):
        start_date_before_but_still_active = Q(start_date__lte=end_date) & Q(end_date__isnull=True)
        start_date_in_range = Q(start_date__gte=start_date) & Q(start_date__lt=end_date)
        end_date_in_range = Q(end_date__isnull=False) & Q(end_date__gte=start_date) & Q(end_date__lt=end_date)
        return self.filter(start_date_in_range | end_date_in_range | start_date_before_but_still_active)


class SimSubscriptionAssignment(models.Model):
    objects = models.Manager.from_queryset(SimSubscriptionAssignmentQuerySet)()

    sim = models.ForeignKey(Sim, on_delete=models.CASCADE, related_name='subscription_assignments')
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE, related_name='sim_assignments')
    start_date = models.DateTimeField()
    end_date = models.DateTimeField(blank=True, null=True)

    def __str__(self):
        return f'{self.sim} assigned to {self.subscription}'

    # class Meta:
    #     ordering = ['-start_date']


class NumberAssignmentQuerySet(models.QuerySet):
    def date_filter(self, start_date, end_date):
        start_date_before_but_still_active = Q(start_date__lte=end_date) & Q(end_date__isnull=True)
        start_date_in_range = Q(start_date__gte=start_date) & Q(start_date__lt=end_date)
        end_date_in_range = Q(end_date__isnull=False) & Q(end_date__gte=start_date) & Q(end_date__lt=end_date)
        return self.filter(start_date_in_range | end_date_in_range | start_date_before_but_still_active)


class NumberAssignment(models.Model):
    objects = models.Manager.from_queryset(NumberAssignmentQuerySet)()

    phone_number = models.CharField(max_length=15, help_text='This needs to match the msisdn, e.g. 447123456789)')
    start_date = models.DateTimeField()
    end_date = models.DateTimeField(blank=True, null=True)
    sim = models.ForeignKey(Sim, on_delete=models.CASCADE, related_name='number_assignments')

    @property
    def msisdn(self):
        return phone_number_to_msisdn(self.phone_number)

    @property
    def is_active(self):
        return self.end_date is None

    @property
    def latest_plan(self):
        return self.sim.latest_plan

    def __str__(self):
        return f'{self.phone_number} assigned to {self.sim} on {self.start_date} to {self.end_date}'

    class Meta:
        ordering = ['-start_date']


class WebhookLog(models.Model):
    class WebhookSource(models.TextChoices):
        TRANSATEL = 'transatel', 'Transatel'
        STRIPE = 'stripe', 'Stripe'
        FAKE_TRANSATEL = 'fake_transatel', 'Fake Transatel'
        FAKE_STRIPE = 'fake_stripe', 'Fake Stripe'
        INFOBIP = 'infobip', 'Infobip'
        GAMMA = 'gamma', 'Gamma'
        SANDBOX = 'sandbox', 'Sandbox'

    date = models.DateTimeField(auto_now_add=True)
    webhook_source = models.CharField(max_length=20, choices=WebhookSource.choices)
    payload = models.JSONField()
    supplemental_data = models.TextField(blank=True)
    client = models.ForeignKey(Client, on_delete=models.CASCADE, blank=True, null=True)

    class Meta:
        verbose_name = 'Webhook Log'
        verbose_name_plural = 'Webhook Logs'
        ordering = ['-date']



class WebhookActionLog(models.Model):
    class ActionStatus(models.TextChoices):
        IN_PROGRESS = 'in_progress', 'In Progress'
        ERRORED_UNKNOWN = 'errored_unknown', 'Errored - Unknown'
        ERRORED_KNOWN = 'errored_known', 'Errored - Known'
        COMPLETED = 'completed', 'Completed'

    log = models.TextField(default='')
    status = models.CharField(max_length=20, choices=ActionStatus.choices)
    webhook_log = models.ForeignKey(WebhookLog, on_delete=models.CASCADE)

    @property
    def client(self):
        return self.webhook_log.client

    @property
    def payload(self):
        return self.webhook_log.payload

    @property
    def date(self):
        return self.webhook_log.date

    def add_log_entry(self, msg):
        timestamp = timezone.now().strftime('%Y-%m-%d %H:%M:%S')
        entry = f"{timestamp} UTC: {msg}\n"
        self.log += entry
        self.save(update_fields=['log'])

    def fail(self, msg):
        self.add_log_entry(msg)
        self.status = WebhookActionLog.ActionStatus.ERRORED_UNKNOWN
        self.save()

    class Meta:
        verbose_name = 'Webhook Action Log'
        verbose_name_plural = 'Webhook Action Logs'


class PACRequest(models.Model):
    pac_code = models.CharField(max_length=30)
    phone_number = models.CharField(max_length=30)
    desired_date = models.DateField(null=True, blank=True)
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE, related_name='pac_requests')
    created = models.DateField(auto_now_add=True)

    @property
    def expected_date(self):
        if self.desired_date and self.desired_date > self.created:
            return self.desired_date
        return self.created + timedelta(days=1)

    @property
    def msisdn(self):
        return phone_number_to_msisdn(self.phone_number)

    def __str__(self):
        return f'PAC Request for {self.subscription}'

    @property
    def port_in_details(self):  # API interop
        return {
            'pac_code': self.pac_code,
            'incoming_phone_number': self.msisdn,
            'desired_date': self.desired_date.isoformat() if self.desired_date else None,  # pylint: disable=no-member
        }

    @property
    def reference_id(self):
        if self.port_in_journey:  # pylint: disable=no-member
            return self.port_in_journey.journey.reference_id  # pylint: disable=no-member
        return None

    class Meta:
        verbose_name = 'PAC Request'
        verbose_name_plural = 'PAC Requests'
        ordering = ['-id']


class PortabilityEvent(models.Model):
    sim = models.ForeignKey(Sim, on_delete=models.CASCADE, related_name='portability_events')
    status = models.CharField(max_length=20)
    msisdn = models.CharField(max_length=30)

    @property
    def phone_number(self):
        return phone_number_to_msisdn(self.msisdn)

    @property
    def simple_status(self):
        return {
            "ATTACH_FAILED": 'errored',
            "ATTACH_RECEIVED": 'in-progress',
            "ATTACH_REQUESTED": 'in-progress',
            "CANCELED": 'errored',
            "CANCELING": 'errored',
            "CODE_CANCELED": 'errored',
            "CODE_EXPIRED": 'errored',
            "CODE_PROVIDED": 'errored',
            "DONE": 'done',
            "ERROR": 'errored',
            "INVALIDATED": 'errored',
            "LOCKED": 'errored',
            "PENDING": 'in-progress',
            "VALIDATED": 'in-progress',

            'OPEN': 'in-progress',
            'CLOSED': 'in-progress',
            'ARCHIVED': 'done',
            'CANCELLED': 'errored',
            'EXPIRED': 'errored',
            'gammma-LOCKED': 'in-progress',

            "ERRORED": 'errored'
        }.get(self.status, 'errored')

    class Meta:
        unique_together = ['sim', 'msisdn']


class EmailType(models.Model):
    name = models.CharField(max_length=255, unique=True)

    def __str__(self):
        return self.name


class EmailConfiguration(models.Model):
    class EmailProvider(models.TextChoices):
        SMTP = 'smtp', 'SMTP'
        FAKE = 'fake', 'Fake'
    client = models.OneToOneField('Client', on_delete=models.CASCADE, related_name='email_config')
    provider = models.CharField(
        max_length=4,
        choices=EmailProvider.choices,
        default=EmailProvider.SMTP
    )
    identity = models.CharField(max_length=255, blank=True, null=True)
    credential = models.CharField(max_length=255, blank=True, null=True)
    endpoint = models.CharField(max_length=255, blank=True, null=True)
    from_address = models.EmailField()
    email_types = models.ManyToManyField(EmailType, help_text='Types of emails that can be sent using this configuration')
    client_name = models.CharField(max_length=255, blank=True, null=True, help_text='Name of the client (if different)')
    client_logo_url = models.URLField(help_text='URL to the client logo')
    client_mobile_name = models.CharField(max_length=255, blank=True, null=True, help_text='Name of the client in mobile style e.g. Six Mobile')
    client_footer = models.TextField(help_text='Footer for the client')
    support_email = models.EmailField(help_text='Support email for the client')
    support_link = models.CharField(max_length=255, blank=True, help_text='Support link to help center for the client')
    help_esim_install_link = models.CharField(max_length=255, blank=True, help_text='Support link to help center for the client for esim install')
    help_keep_number_link = models.CharField(max_length=255, blank=True, help_text='Support link to help center for the client for keep number')
    help_data_issue = models.CharField(max_length=255, blank=True, help_text='Support link to help center for the client in case of data issues')
    debug_bcc = models.EmailField(blank=True, null=True, help_text='Email to BCC on all emails for debugging')
    plan_color_hex = models.CharField(max_length=7, default='#FFFFFF', help_text='Hex color for the plan, e.g. #123456')
    plan_text_color_hex = models.CharField(max_length=7, default='#FFFFFF', help_text='Hex color for the plan text, e.g. #123456')


    def __str__(self):
        return f"{self.client.name} - {self.provider}"


class SystemEmail(models.Model):
    class EmailStatus(models.TextChoices):
        PENDING = 'pending', 'Pending'
        SENDING = 'sending', 'Sending'
        SENT = 'sent', 'Sent'
        ERRORED = 'errored', 'Errored'

    created = models.DateTimeField(default=timezone.now)
    sent_at = models.DateTimeField(null=True, blank=True)
    recipient = models.EmailField()
    recipient_key = models.CharField(max_length=255, db_index=True)
    email_type = models.ForeignKey('EmailType', on_delete=models.CASCADE)
    client = models.ForeignKey('Client', on_delete=models.CASCADE)
    params = models.JSONField(default=dict)
    status = models.CharField(
        max_length=7,
        choices=EmailStatus.choices,
        default=EmailStatus.PENDING
    )

    @property
    def attachments(self):
        raw_attachments = self.params.get('attachments', [])
        attachments = []
        for attachment in raw_attachments:
            subtype = attachment.get('subtype')
            if subtype in ['png']:
                raw_bytes = base64.b64decode(attachment['bytes_as_base64'])
                mime_image = MIMEImage(raw_bytes, _subtype=subtype)
                for header_arg in attachment.get('header_args', []):
                    if isinstance(header_arg, (list, tuple)):
                        mime_image.add_header(*header_arg)
                    else:
                        mime_image.add_header(**header_arg)
                attachments.append(mime_image)
            else:
                raise RuntimeError(f'Unknown attachment subtype {subtype}')
        return attachments

    class Meta:
        unique_together = ('recipient_key', 'email_type')

    def __str__(self):
        return f"{self.email_type.name} to {self.recipient} ({self.status})"


class MarketingEligibility(models.Model):
    class EligibilityType(models.TextChoices):
        SUBSCRIBER_ACTIVITY = 'subscriber_activity', 'Existing Subscribers'
        NEW_SUBSCRIBERS = 'new_subscribers', 'New Subscribers'

    eligibility_type = models.CharField(
        max_length=20,
        choices=EligibilityType.choices
    )
    active_since = models.DateTimeField(null=True, blank=True)
    active_duration_months = models.PositiveIntegerField(default=0)

    def get_relevant_subscriptions(self, client):
        if self.eligibility_type == MarketingEligibility.EligibilityType.NEW_SUBSCRIBERS:
            return []
        elif self.eligibility_type == MarketingEligibility.EligibilityType.SUBSCRIBER_ACTIVITY:
            if self.active_since:
                return client.all_active_subscriptions().active_since(self.active_since)
            elif self.active_duration_months:
                return client.all_active_subscriptions().active_for(months=self.active_duration_months)
            else:
                return client.all_active_subscriptions().all()
        return []

    @staticmethod
    def create_eligibility_for_all_subscribers():
        return MarketingEligibility.objects.create(eligibility_type=MarketingEligibility.EligibilityType.SUBSCRIBER_ACTIVITY)

    def __str__(self):
        if self.eligibility_type == MarketingEligibility.EligibilityType.NEW_SUBSCRIBERS:
            return "New Subscribers"
        elif self.eligibility_type == MarketingEligibility.EligibilityType.SUBSCRIBER_ACTIVITY:
            active_fragment = ''
            duration_fragment = ''
            if self.active_since:
                active_fragment = f" since {self.active_since.strftime('%Y-%m-%d')}"  # pylint:disable=no-member
            if self.active_duration_months:
                duration_fragment = f" for {self.active_duration_months} month{'s' if self.active_duration_months > 1 else ''}"
            if active_fragment or duration_fragment:
                return f"Subscribers active{active_fragment}{duration_fragment}"
            else:
                return "All Active Subscribers"
        else:
            return "Unknown eligibility"

    class Meta:
        indexes = [
            models.Index(fields=['eligibility_type']),
        ]
        ordering = ['-id']


class SMSMessage(models.Model):
    class Status(models.TextChoices):
        TO_SEND = 'to_send', 'To Send'
        SENDING = 'sending', 'Sending'
        SENT = 'sent', 'Sent'
        ERRORED = 'errored', 'Errored'

    title = models.CharField(max_length=255)
    message = models.CharField(max_length=1600)
    send_on = models.DateTimeField(blank=True, null=True)
    status = models.CharField(max_length=10, choices=Status.choices, default=Status.TO_SEND)
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    initiator = models.ForeignKey(DashboardUser, on_delete=models.CASCADE)
    created = models.DateTimeField(auto_now_add=True)
    marketing_eligibility = models.OneToOneField(MarketingEligibility, on_delete=models.CASCADE)
    campaign = models.ForeignKey('Campaign', on_delete=models.CASCADE, blank=True, null=True)

    def get_all_recipients(self):
        subscriptions = self.marketing_eligibility.get_relevant_subscriptions(self.client).filter(subscriber__send_marketing=True)
        for subscription in subscriptions:
            number = subscription.uk_prefixed_phone_number
            if number:
                yield number

    def should_send_now(self):
        return self.status == SMSMessage.Status.TO_SEND and (self.send_on is None or self.send_on <= timezone.now())

    @property
    def sender(self):
        return self.client.sms_config.sender  # pylint: disable=no-member

    @property
    def delivery_report(self):
        report = self.individual_deliveries.aggregate(
            total_sent=Count('id'),
            total_delivered=Count('id', filter=Q(status=IndividualSMSDelivery.Status.DELIVERED)),
            total_failed=Count('id', filter=Q(status=IndividualSMSDelivery.Status.FAILED)),
            total_pending=Count('id', filter=Q(status=IndividualSMSDelivery.Status.PENDING)),
            total_units_sent=Sum('units_sent'),
        )
        report['delivery_rate'] = ((report['total_delivered'] / report['total_sent']) * 100) if report['total_sent'] > 0 else 0
        report['sms_message_id'] = self.id
        report['sender'] = self.sender
        report['send_date'] = self.created
        return report

    class Meta:
        ordering = ['-created']

    def __str__(self):
        return f"SMS for {self.client} ({self.status})"


class SMSConfiguration(models.Model):
    class SMSProvider(models.TextChoices):
        FAKE = 'fake', 'Fake'
        INFOBIP = 'infobip', 'Infobip'
    client = models.OneToOneField('Client', on_delete=models.CASCADE, related_name='sms_config')
    provider = models.CharField(
        max_length=10,
        choices=SMSProvider.choices,
        default=SMSProvider.FAKE
    )
    allow_out_of_hours_sending = models.BooleanField(default=False)
    identity = models.CharField(max_length=255, blank=True, null=True)
    credential = models.CharField(max_length=255, blank=True, null=True)
    endpoint = models.CharField(max_length=255, blank=True, null=True)
    sender = models.CharField(max_length=255)
    delivery_report_url = models.URLField()

    def __str__(self):
        return f"{self.client.name} - {self.provider}"


class IndividualSMSDelivery(models.Model):
    class Status(models.TextChoices):
        DELIVERED = 'delivered', 'Delivered'
        PENDING = 'pending', 'Pending'
        FAILED = 'failed', 'Failed'

    sms_message = models.ForeignKey(SMSMessage, on_delete=models.CASCADE, related_name='individual_deliveries')
    status = models.CharField(max_length=20, choices=Status.choices, default=Status.PENDING)
    recipient = models.CharField(max_length=30)
    units_sent = models.IntegerField(default=0)

    @staticmethod
    def create_for_recipients(sms_message, recipients):
        return IndividualSMSDelivery.objects.bulk_create([IndividualSMSDelivery(recipient=recipient, sms_message=sms_message) for recipient in recipients])

    class Meta:
        unique_together = ('sms_message', 'recipient')
        indexes = [
            models.Index(fields=['recipient']),
            models.Index(fields=['sms_message', 'recipient', 'status']),
            models.Index(fields=['sms_message', 'recipient']),
        ]


class CampaignQuerySet(models.QuerySet):
    def active(self):
        return self.filter(status=Campaign.Status.ACTIVE, start_date__lte=timezone.now(), end_date__gte=timezone.now())

    def applies_to_new_subscribers(self):
        return self.filter(marketing_eligibility__eligibility_type=MarketingEligibility.EligibilityType.NEW_SUBSCRIBERS)

    def applies_to_existing_subscribers(self):
        return self.filter(marketing_eligibility__eligibility_type=MarketingEligibility.EligibilityType.SUBSCRIBER_ACTIVITY)


class Campaign(models.Model):
    class CampaignType(models.TextChoices):
        DISCOUNT = 'discount', 'Discount'

    class Status(models.TextChoices):
        DRAFT = 'draft', 'Draft'
        READY = 'ready', 'Ready'
        ACTIVE = 'active', 'Active'
        FINISHED = 'finished', 'Finished'
        CANCELLED = 'cancelled', 'Cancelled'

    title = models.CharField(max_length=255)
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='campaigns')
    marketing_eligibility = models.OneToOneField(MarketingEligibility, on_delete=models.CASCADE)
    campaign_type = models.CharField(
        max_length=20,
        choices=CampaignType.choices
    )
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.DRAFT
    )

    objects = models.Manager.from_queryset(CampaignQuerySet)()

    def set_appropriate_status(self):
        should_be_active = self.start_date <= timezone.now() and timezone.now() <= self.end_date
        new_status = None
        if should_be_active and self.status == Campaign.Status.READY:
            new_status = Campaign.Status.ACTIVE
        if not should_be_active and self.status == Campaign.Status.ACTIVE:
            new_status = Campaign.Status.FINISHED
        if new_status:
            self.status = new_status
            self.save()

    @property
    def discount_type_summary(self):
        discount_types = set()
        for plan_discount in self.plan_discounts.active():  # pylint: disable=no-member
            discount_types.add(plan_discount.discount_type_display)
        return ', '.join(discount_types)

    @property
    def discount_value_summary(self):
        discount_values = set()
        for plan_discount in self.plan_discounts.active():  # pylint: disable=no-member
            discount_values.add(plan_discount.discount_value_display)
        return ', '.join(discount_values)

    def __str__(self):
        return f"{self.title}"

    class Meta:
        indexes = [
            models.Index(fields=['status']),
        ]
        ordering = ['-start_date']

class PlanDiscountQuerySet(models.QuerySet):
    def active(self):
        return self.filter(is_deleted=False)

    def for_campaigns(self, campaigns):
        return self.filter(campaign__in=campaigns)

    def applicable_to_plan(self, plan):
        return self.active().filter(Q(plan=plan) | Q(plan__isnull=True))

    def flat(self):
        return self.filter(discount_type=PlanDiscount.DiscountType.FLAT)

    def percentage(self):
        return self.filter(discount_type=PlanDiscount.DiscountType.PERCENTAGE)


class PlanDiscount(models.Model):
    class DiscountType(models.TextChoices):
        PERCENTAGE = 'percentage', 'Percentage discount'
        FLAT = 'flat', 'Flat discount'

    campaign = models.ForeignKey('Campaign', on_delete=models.SET_NULL, related_name='plan_discounts', blank=True, null=True)
    discount_perk = models.ForeignKey('DiscountPerk', on_delete=models.SET_NULL, related_name='plan_discounts', blank=True, null=True)
    plan = models.ForeignKey(Plan, on_delete=models.SET_NULL, related_name='discounts', blank=True, null=True)
    discount_type = models.CharField(max_length=10, choices=DiscountType.choices)
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    discount_duration_months = models.IntegerField(help_text='0 is unlimited', default=0)
    billing_discount_id = models.CharField(max_length=255, blank=True, null=True)
    is_deleted = models.BooleanField(default=False)
    limit_to_plan = models.BooleanField(default=False, help_text="Only works once, during creation")

    objects = models.Manager.from_queryset(PlanDiscountQuerySet)()

    def mark_as_deleted(self):
        self.is_deleted = True
        self.save()

    @property
    def end_date_from_now(self):
        if self.is_unlimited:
            return None
        return timezone.now() + relativedelta(months=self.discount_duration_months)

    def apply_to_subscription(self, subscription):
        if subscription.latest_plan == self.plan or not self.plan:
            application = DiscountApplication.objects.create(subscription=subscription, plan_discount=self, ends_at=self.end_date_from_now)
            application.apply_to_billing_if_necessary()
            send_debug_slack_message(f"Plan discount {self.pk} applied to subscription {subscription.pk} ({subscription}) for {subscription.client}")
        else:
            send_debug_slack_message(f"Plan discount {self.pk} not applied to subscription {subscription.pk} ({subscription}) as the plan does not match (for {subscription.client})")

    @property
    def client(self):
        if self.plan:
            return self.plan.client
        if self.campaign:
            return self.campaign.client
        if self.discount_perk:
            return self.discount_perk.client
        if application := self.discount_applications.first():  # pylint: disable=no-member
            return application.client
        if client := self.client_additional_discounts.first():  # pylint: disable=no-member
            return client
        return None

    def sync_to_gateway_if_necessary(self):
        if not self.client.plans_assigned_externally:
            if self.client.has_payment_integration and self.client.payment_integration.gateway == PaymentIntegration.Gateway.STRIPE:  # pylint: disable=no-member
                stripe_client = get_stripe_client(self.client.payment_integration.secret_credentials)  # pylint: disable=no-member
                params = {'name': self.discount_value_display, 'metadata': {'discount_id': self.pk}}
                if self.discount_type == PlanDiscount.DiscountType.FLAT:
                    params['amount_off'] = int(self.discount_amount * 100)
                    params['currency'] = 'gbp'
                elif self.discount_type == PlanDiscount.DiscountType.PERCENTAGE:
                    params['percent_off'] = self.discount_percentage
                if self.is_unlimited:
                    params['duration'] = 'forever'
                elif self.is_once:
                    params['duration'] = 'once'
                else:
                    params['duration'] = 'repeating'
                    params['duration_in_months'] = self.discount_duration_months

                if self.limit_to_plan and self.plan:
                    product_id = self.plan.get_billing_product_id()
                    if product_id:
                        params['applies_to'] = {'products': [product_id]}
                coupon = stripe_client.coupons.create(params=params)
                self.billing_discount_id = coupon.id
                self.save()

    @property
    def discount_value_display(self):
        if self.discount_type == PlanDiscount.DiscountType.PERCENTAGE:
            return f"{self.discount_percentage}% off"
        return f"£{self.discount_amount} off"

    @property
    def discount_type_display(self):
        return self.get_discount_type_display()

    @property
    def is_unlimited(self):
        return self.discount_duration_months == 0

    @property
    def is_once(self):
        return self.discount_duration_months == 1

    def __str__(self):
        return f"{self.get_discount_type_display()} discount on {self.plan.name if self.plan else 'all plans'} for {self.campaign.title if self.campaign else self.discount_perk}"

    @property
    def subtracting_multiplier(self):
        return 1 - (self.discount_percentage / 100)

class DiscountApplicationQuerySet(models.QuerySet):
    def active(self):
        return self.filter(Q(ends_at__lte=timezone.now()) | Q(ends_at__isnull=True))

    def flat(self):
        return self.filter(plan_discount__discount_type=PlanDiscount.DiscountType.FLAT)

    def percentage(self):
        return self.filter(plan_discount__discount_type=PlanDiscount.DiscountType.PERCENTAGE)

class DiscountApplication(models.Model):
    subscription = models.ForeignKey('Subscription', on_delete=models.CASCADE, related_name='discount_applications')
    plan_discount = models.ForeignKey('PlanDiscount', on_delete=models.CASCADE, related_name='discount_applications')
    applied_at = models.DateTimeField(auto_now_add=True)
    ends_at = models.DateTimeField(blank=True, null=True, help_text='If null, the discount is unlimited')

    objects = models.Manager.from_queryset(DiscountApplicationQuerySet)()

    @property
    def discount_amount(self):
        return self.plan_discount.discount_amount

    @property
    def subtracting_multiplier(self):
        return self.plan_discount.subtracting_multiplier

    @property
    def client(self):
        return self.subscription.client

    def apply_to_billing_if_necessary(self):
        print('applying', self, 'to', self.subscription.billing_subscription_id, 'with', self.plan_discount.billing_discount_id)
        if not self.client.plans_assigned_externally:
            if self.plan_discount.billing_discount_id:
                if self.client.has_payment_integration and self.client.payment_integration.gateway == PaymentIntegration.Gateway.STRIPE:  # pylint: disable=no-member
                    stripe_client = get_stripe_client(self.client.payment_integration.secret_credentials)  # pylint: disable=no-member
                    subscription = stripe_client.subscriptions.retrieve(self.subscription.billing_subscription_id, params={'expand': ['discounts']})
                    existing_discounts = subscription['discounts'] or []
                    if existing_discounts:
                        existing_discounts = [{'coupon': di['coupon']['id']} for di in existing_discounts]
                    existing_discounts.append({'coupon': self.plan_discount.billing_discount_id})
                    stripe_client.subscriptions.update(subscription['id'], params={'discounts': existing_discounts})
            else:
                send_debug_slack_message(f"Plan discount {self.plan_discount.pk} applied to subscription {self.subscription.pk} ({self.subscription}) but no billing discount id")


    def __str__(self):
        return f"{self.plan_discount} applied to {self.subscription}"


class PlanChangeQuerySet(models.QuerySet):
    def active(self):
        return self.filter(status__in=[PlanChange.Status.IN_PROGRESS, PlanChange.Status.LOCKED])

    def active_or_completed(self):
        return self.filter(status__in=[PlanChange.Status.IN_PROGRESS, PlanChange.Status.LOCKED, PlanChange.Status.COMPLETE])

    def not_cancelled(self):
        return self.filter(cancellation__isnull=True)


class PlanChange(models.Model):
    class ChangeType(models.TextChoices):
        UPGRADE = 'upgrade', 'Upgrade'
        DOWNGRADE = 'downgrade', 'Downgrade'
        CANCEL_CHANGE = 'cancel_change', 'Cancel Change'
        CANCELLATION = 'cancellation', 'Cancellation'

    class Status(models.TextChoices):
        IN_PROGRESS = 'in_progress', 'In Progress'
        LOCKED = 'locked', 'Locked'
        COMPLETE = 'complete', 'Complete'
        CANCELLED = 'cancelled', 'Cancelled'
        ERRORED = 'errored', 'Errored'

    objects = models.Manager.from_queryset(PlanChangeQuerySet)()

    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE, related_name='plan_changes')
    change_type = models.CharField(max_length=20, choices=ChangeType.choices)
    target_plan = models.ForeignKey(Plan, on_delete=models.CASCADE, blank=True, null=True)
    target_plan_change = models.OneToOneField('PlanChange', on_delete=models.CASCADE, blank=True, null=True, related_name='cancellation')
    execution_start_time = models.DateTimeField(blank=True, null=True)
    status = models.CharField(max_length=15, choices=Status.choices, default=Status.IN_PROGRESS)
    execution_id = models.CharField(max_length=255, blank=True, null=True)
    sub_execution_id = models.CharField(max_length=255, blank=True, null=True)
    task_token = models.CharField(max_length=255, blank=True, null=True)
    billing_reference = models.CharField(max_length=255, blank=True, null=True)

    @property
    def future_execution_timestamp(self):
        if self.change_type == self.ChangeType.DOWNGRADE:
            return self.subscription.billing_cycle_freeze_date
        return None

    @property
    def completion_expected_timestamp(self):
        if self.change_type == self.ChangeType.DOWNGRADE:
            return self.subscription.latest_provider.get_billing_month_date_range_containing(self.execution_start_time)[1] + timedelta(hours=settings.BILLING_CYCLE_EXPECT_PAYMENT_AFTER_HOURS)
        return None

    @property
    def can_be_cancelled(self):
        return self.status == self.Status.IN_PROGRESS and self.change_type in [self.ChangeType.CANCELLATION, self.ChangeType.DOWNGRADE] and not self.subscription.is_too_close_to_billing_cycle_to_change

    @property
    def subscriber_status_display(self):  # pylint: disable=too-many-return-statements
        if self.status == self.Status.IN_PROGRESS:
            return 'Pending'
        elif self.status == self.Status.LOCKED:
            if self.change_type == self.ChangeType.CANCELLATION:
                return 'Cancelling'
            elif self.change_type == self.ChangeType.DOWNGRADE:
                return 'Downgrading'
            elif self.change_type == self.ChangeType.UPGRADE:
                return 'Upgrading'
            elif self.change_type == self.ChangeType.CANCEL_CHANGE:
                return 'Cancelling Change'
        elif self.status == self.Status.COMPLETE:
            return 'Complete'
        elif self.status == self.Status.CANCELLED:
            return 'Cancelled'
        elif self.status == self.Status.ERRORED:
            return 'Errored'
        return 'Changing'

    def __str__(self):
        return f"{self.subscription} - {self.get_change_type_display()}"

    class Meta:
        verbose_name = "Plan Change"
        verbose_name_plural = "Plan Changes"
        ordering = ['-id']



class SubscriberGroup(models.Model):
    name = models.CharField(max_length=255)
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    subscribers = models.ManyToManyField(Subscriber, related_name='groups')

    def __str__(self):
        return self.name


class Perk(PolymorphicModel):
    class Meta:
        ordering = ['-id']
    class EligibilityType(models.TextChoices):
        TENURE = 'tenure', 'Tenure'
        TOTAL_SPEND = 'total_spend', 'Total Spend'
        TOTAL_POINTS_EARNED = 'total_points_earned', 'Total Points Earned'
        AIRDROP = 'airdrop', 'Airdrop'
        NO_FREE = 'no_free', 'No Free'

    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='perks')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    redeem_details = models.TextField(blank=True, null=True)
    cost_base = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    perk_image = models.TextField(blank=True, null=True)
    perk_image_link = models.URLField(blank=True, null=True)
    perk_logo_link = models.URLField(blank=True, null=True)
    eligibility_type = models.CharField(max_length=40, choices=EligibilityType.choices)
    eligibility_threshold = models.DecimalField(max_digits=10, decimal_places=2, default=0, help_text='Points, months, spend in pounds, etc')
    target_group = models.ForeignKey(SubscriberGroup, on_delete=models.SET_NULL, null=True, blank=True, help_text='Unset means target everyone')
    allow_multiple_redemptions = models.BooleanField(default=False)
    electively_redeemable = models.BooleanField(default=False)
    elective_redemption_cost = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    availability_date = models.DateTimeField(blank=True, null=True, help_text='Blank means immediately')
    redemption_limit = models.IntegerField(default=0, help_text='0 means unlimited')
    featured = models.BooleanField(default=False)
    enabled = models.BooleanField(default=False)

    @property
    def can_currently_be_redeemed_for_points(self):
        if self._is_redeemable:
            return self.points_cost
        return False

    @property
    def can_currently_be_redeemed_via_eligibility(self):
        return self._is_redeemable

    @property
    def _is_redeemable(self):
        return self.enabled and (self.availability_date is None or timezone.now() >= self.availability_date) and (self.allows_unlimited_redemptions or self.remaining_quantity > 0)

    @contextmanager
    def lock(self):
        with transaction.atomic():
            perk = Perk.objects.select_for_update().get(pk=self.pk)
            yield perk

    def __str__(self):
        return self.name

    @property
    def is_discount(self):
        return isinstance(self, DiscountPerk)

    @property
    def is_voucher(self):
        return isinstance(self, VoucherPerk)

    @property
    def is_bolt_on(self):
        return isinstance(self, BoltOnPerk)

    @property
    def redemptions_amount(self):
        return self.redemptions.count()

    @property
    def remaining_quantity(self):
        return 0 if self.allows_unlimited_redemptions else (self.redemption_limit - self.redemptions_amount)

    @property
    def total_cost(self):
        if self.cost_base is not None:
            return self.cost_base * self.redemptions_amount
        return 0

    @property
    def allows_unlimited_redemptions(self):
        return self.redemption_limit == 0

    @property
    def title(self):
        if self.is_bolt_on:
            return self.bolt_on.display_name
        else:
            return self.name

    @property
    def details(self):
        if self.is_voucher:
            return self.voucher_details
        else:
            return None

    def subscriber_meets_eligibility(self, subscriber):
        if self.eligibility_type == Perk.EligibilityType.TENURE:
            return subscriber.tenure_value >= self.eligibility_threshold
        elif self.eligibility_type == Perk.EligibilityType.TOTAL_SPEND:
            return subscriber.total_spend >= self.eligibility_threshold
        elif self.eligibility_type == Perk.EligibilityType.TOTAL_POINTS_EARNED:
            return subscriber.total_points_earned >= self.eligibility_threshold
        elif self.eligibility_type == Perk.EligibilityType.NO_FREE:
            return subscriber.perk_points > self.points_cost
        elif self.eligibility_type == Perk.EligibilityType.AIRDROP:
            return True
        return False

    def redeem_for_subscriber(self, subscriber):
        send_debug_slack_message(f"Redeeming {self} for {subscriber} but no redemption method defined")

    @property
    def points_cost(self):
        if self.eligibility_type == Perk.EligibilityType.NO_FREE:
            if self.eligibility_threshold is not None:
                if self.electively_redeemable and self.elective_redemption_cost is not None:
                    return min(self.elective_redemption_cost, self.eligibility_threshold)
                else:
                    return self.eligibility_threshold
        if self.electively_redeemable:
            return self.elective_redemption_cost
        return None

class DiscountPerk(Perk):
    @property
    def plan_discount(self):
        return self.plan_discounts.active().first()  # pylint: disable=no-member

    @property
    def discount_details(self):
        return {'plan_discount': self.plan_discount}

    def __str__(self):
        return f"{self.name} (Discount Perk)"

    def redeem_for_subscriber(self, subscriber):
        self.plan_discount.apply_to_subscription(subscriber.subscriptions.first())


class VoucherPerk(Perk):
    code = models.CharField(max_length=200)
    url = models.URLField(blank=True, null=True)
    instructions = models.TextField(blank=True, null=True)
    expiry_date = models.DateTimeField(blank=True, null=True)
    merchant_name = models.CharField(max_length=100, blank=True, null=True)

    @property
    def voucher_details(self):
        return {'code': self.code, 'url': self.url, 'instructions': self.instructions, 'expiry_date': self.expiry_date, 'merchant_name': self.merchant_name}

    def __str__(self):
        return f"{self.name} (Voucher Perk)"

class BoltOnPerk(Perk):
    bolt_on = models.ForeignKey('ClientBoltOn', on_delete=models.CASCADE)

    @property
    def bolt_on_details(self):
        return {'bolt_on': self.bolt_on}

    def __str__(self):
        return f"{self.name} (Bolt On Perk)"

class PerkRedemptionQuerySet(models.QuerySet):
    def date_filter(self, start_date, end_date):
        queries = []
        if start_date is not None:
            queries.append(Q(redeemed_on__gte=start_date))
        if end_date is not None:
            queries.append(Q(redeemed_on__lt=end_date))
        return self.filter(*queries)

    def for_client(self, client):
        return self.filter(perk__client=client)

class PerkRedemption(models.Model):
    class FulfilmentStatus(models.TextChoices):
        PENDING = 'pending', 'Pending'
        FULFILLED = 'fulfilled', 'Fulfilled'
        ERRORED = 'errored', 'Errored'
        AWAITING_CONFIRMATION = 'awaiting_confirmation', 'Awaiting Confirmation'
    objects = models.Manager.from_queryset(PerkRedemptionQuerySet)()
    perk = models.ForeignKey(Perk, on_delete=models.CASCADE, related_name='redemptions')
    subscriber = models.ForeignKey(Subscriber, on_delete=models.CASCADE, related_name='perk_redemptions')
    redeemed_on = models.DateTimeField(auto_now_add=True)
    points_paid = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    fulfilment_status = models.CharField(max_length=25, choices=FulfilmentStatus.choices, default=FulfilmentStatus.PENDING)
    remaining_quantity = models.IntegerField(default=0, blank=True, null=True)
    is_confirmed = models.BooleanField(
        default=True,
        help_text='Whether this redemption has been confirmed. Auto-confirmed if not required by client.'
    )

    @staticmethod
    def claim_via_points(redeemer, target_perk):
        with target_perk.lock() as perk:
            with redeemer.lock() as subscriber:
                if perk.can_currently_be_redeemed_for_points and subscriber.can_afford_perk(perk):
                    points = perk.points_cost
                    subscriber.deduct_points(points)
                    redemption = PerkRedemption._create(
                        subscriber=subscriber,
                        perk=perk,
                        points_paid=points
                    )
                    if not redemption.is_confirmed:
                        redemption.fulfilment_status = PerkRedemption.FulfilmentStatus.AWAITING_CONFIRMATION
                        redemption.save()
                    else:
                        perk.redeem_for_subscriber(subscriber)
                    return redemption
            return None

    @staticmethod
    def claim_via_eligibility(redeemer, target_perk):
        with target_perk.lock() as perk:
            with redeemer.lock() as subscriber:
                if perk.can_currently_be_redeemed_via_eligibility and perk.subscriber_meets_eligibility(subscriber):
                    redemption = PerkRedemption._create(
                        subscriber=subscriber,
                        perk=perk
                    )
                    if not redemption.is_confirmed:
                        redemption.fulfilment_status = PerkRedemption.FulfilmentStatus.AWAITING_CONFIRMATION
                        redemption.save()
                    else:
                        perk.redeem_for_subscriber(subscriber)
                    return redemption
            return None

    @staticmethod
    def _create(subscriber, perk, points_paid=None):
        redemption = PerkRedemption.objects.create(
            subscriber=subscriber,
            perk=perk,
            points_paid=points_paid,
            remaining_quantity=perk.remaining_quantity if perk.allows_unlimited_redemptions else perk.remaining_quantity - 1,
            is_confirmed = subscriber.client.name.lower() not in settings.REQUIRE_PERK_REDEMPTION_CONFIRMATION
        )
        return redemption

    def confirm(self):
        if not self.is_confirmed and self.fulfilment_status == PerkRedemption.FulfilmentStatus.AWAITING_CONFIRMATION:
            self.is_confirmed = True
            self.fulfilment_status = PerkRedemption.FulfilmentStatus.PENDING
            self.save()
            self.perk.redeem_for_subscriber(self.subscriber)
            return True
        return False

    @property
    def cost(self):
        return self.perk.cost_base

    def __str__(self):
        return f"{self.perk} redeemed by {self.subscriber} on {self.redeemed_on}"


class DemoUsage(models.Model):
    class Dimension(models.TextChoices):
        SMS = 'sms', 'SMS'
        VOICE = 'voice', 'Voice'
        DATA = 'data', 'Data'

    def clean(self):
        # Assert client's provider is demo and sim's client is this client
        if not self.client.provider.is_demo:
            raise ValidationError('Client provider is not demo')
        if self.sim.client != self.client:
            raise ValidationError('Sim client does not match client')

    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    sim = models.ForeignKey(Sim, on_delete=models.CASCADE)
    dimension = models.CharField(max_length=10, choices=Dimension.choices)
    value = models.FloatField()

    def __str__(self):
        return f"{self.client} - {self.dimension} - {self.value}"


class BoltOn(models.Model):
    class BoltOnType(models.TextChoices):
        STACKABLE = 'stackable', 'Stackable'
        BINARY = 'binary', 'Binary'

    class DurationType(models.TextChoices):
        DAY = 'day', 'Day'
        WEEK = 'week', 'Week'
        MONTH = 'month', 'Month'

    name = models.CharField(max_length=100)
    provider_code = models.CharField(max_length=50)
    provider = models.ForeignKey(Provider, on_delete=models.CASCADE)
    description = models.TextField()
    default_price = models.DecimalField(max_digits=10, decimal_places=2, help_text="Price we charge to our clients")
    default_cost = models.DecimalField(max_digits=10, decimal_places=2, help_text="Price it costs us, used for margin calculation")
    bolt_on_type = models.CharField(max_length=10, choices=BoltOnType.choices)
    duration_type = models.CharField(max_length=10, choices=DurationType.choices)
    billing_calendar_sync = models.BooleanField(help_text="Whether it syncs itself to provider billing days or not")
    data_gb = models.FloatField(blank=True, default=0)
    voice_minutes = models.FloatField(blank=True, default=0)
    sms = models.IntegerField(blank=True, default=0)
    roaming = models.BooleanField(default=False, help_text='Is this a roaming bolt-on?')
    roaming_zone = models.CharField(max_length=50, blank=True, null=True, help_text='Lower case, e=EU')

    def get_expiry_date(self, start_date):
        if self.duration_type == BoltOn.DurationType.DAY:
            end_date = start_date + timedelta(days=1)
        else:
            end_date = start_date + timedelta(weeks=1)
        return end_date

    @property
    def nearest_upcoming_expiry(self):
        return self.provider.time_control.end_of_today

    def __str__(self):
        return f'{self.name} ({self.provider.name})'

    class Meta:
        ordering = ['-id']
        verbose_name = "Bolt-on"
        verbose_name_plural = "Bolt-ons"


class BoltOnOffering(models.Model):
    bolt_on = models.ForeignKey(BoltOn, on_delete=models.CASCADE)
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    cost_override = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    price_override = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    available_for_new_selection = models.BooleanField(help_text="Whether this bolt-on can be sold/redeemed")

    @property
    def data_gb(self):
        return self.bolt_on.data_gb

    @property
    def voice_minutes(self):
        return self.bolt_on.voice_minutes

    @property
    def sms(self):
        return self.bolt_on.sms

    @property
    def name(self):
        return self.bolt_on.name

    @property
    def min_price(self):
        return self.price_override or self.bolt_on.default_price

    @property
    def roaming(self):
        return self.bolt_on.roaming

    @property
    def roaming_zone(self):
        return self.bolt_on.roaming_zone

    @property
    def nearest_upcoming_expiry(self):
        return self.bolt_on.nearest_upcoming_expiry

    def __str__(self):
        return f"{self.bolt_on.name} for {self.client.name}"

    class Meta:
        ordering = ['-id']
        verbose_name = "Bolt-on Offering"
        verbose_name_plural = "Bolt-on Offerings"


class ClientBoltOnQuerySet(models.QuerySet):
    def enabled(self):
        return self.filter(status=ClientBoltOn.Status.ENABLED)

    def roaming(self):
        return self.filter(offering__bolt_on__roaming=True).exclude(offering__bolt_on__roaming_zone='e')

    def eu_roaming(self):
        return self.filter(offering__bolt_on__roaming=True, offering__bolt_on__roaming_zone='e')


class ClientBoltOn(models.Model):
    class Status(models.TextChoices):
        ENABLED = 'enabled', 'Enabled'
        DISABLED = 'disabled', 'Disabled'

    objects = models.Manager.from_queryset(ClientBoltOnQuerySet)()

    offering = models.ForeignKey(BoltOnOffering, on_delete=models.CASCADE)
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='bolt_ons')
    price = models.DecimalField(max_digits=10, decimal_places=2)
    name_override = models.CharField(max_length=100, blank=True, default='')
    implementation_datetime = models.DateTimeField(default=timezone.now)
    status = models.CharField(max_length=10, choices=Status.choices)
    cost_override = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    bolt_on_key = models.CharField(max_length=50, help_text="Key for grouping the same bolt-on over multiple edits")
    version_number = models.IntegerField(default=0)
    billing_bolt_on_id = models.CharField(max_length=50, blank=True, default='')

    def get_expiry_date(self, start_date):
        return self.offering.bolt_on.get_expiry_date(start_date)

    @property
    def display_name(self):
        return self.name_override or self.offering.bolt_on.name

    @property
    def data_gb(self):
        return self.offering.data_gb

    @property
    def voice_minutes(self):
        return self.offering.voice_minutes

    @property
    def sms(self):
        return self.offering.sms

    @property
    def roaming(self):
        return self.offering.roaming

    @property
    def roaming_zone(self):
        return self.offering.roaming_zone

    @property
    def nearest_upcoming_expiry(self):
        return self.offering.nearest_upcoming_expiry

    def sync_to_gateway_if_necessary(self):
        if not self.client.plans_assigned_externally:
            if self.client.has_payment_integration and self.client.payment_integration.gateway == PaymentIntegration.Gateway.STRIPE:  # pylint: disable=no-member
                stripe_client = get_stripe_client(self.client.payment_integration.secret_credentials)  # pylint: disable=no-member
                if self.billing_bolt_on_id:
                    product = stripe_client.products.retrieve(self.billing_bolt_on_id)
                    stripe_client.products.update(product.id, params={'name': self.display_name})
                    price_id = product.default_price
                    price = stripe_client.prices.retrieve(price_id)
                    if self.price != price.unit_amount / 100:
                        new_price = stripe_client.prices.create(params={'product': product.id, 'unit_amount': int(self.price * 100), 'currency': 'gbp'})
                        stripe_client.products.update(product.id, params={'default_price': new_price.id})
                    self.save()
                else:
                    product = stripe_client.products.create(params={'name': self.display_name})
                    price = stripe_client.prices.create(params={'product': product.id, 'unit_amount': int(self.price * 100), 'currency': 'gbp'})
                    stripe_client.products.update(product.id, params={'default_price': price.id})
                    self.billing_bolt_on_id = product.id
                    self.save()

    @property
    def billing_price_id(self):
        if not self.client.plans_assigned_externally:
            if self.client.has_payment_integration and self.client.payment_integration.gateway == PaymentIntegration.Gateway.STRIPE:  # pylint: disable=no-member
                stripe_client = get_stripe_client(self.client.payment_integration.secret_credentials)  # pylint: disable=no-member
                product = stripe_client.products.retrieve(self.billing_bolt_on_id)
                return product.default_price
        return None

    @property
    def min_price(self):
        return self.offering.min_price

    def clean(self):
        if self.offering.client != self.client:
            raise ValidationError('ClientBoltOn client must match BoltOnOffering client')

    def __str__(self):
        return f"{self.name_override or self.offering.bolt_on.name} ({self.client.name})"


class BoltOnPurchaseQuerySet(models.QuerySet):
    def active(self):
        return self.filter(Q(expiry__isnull=True) | Q(expiry__gt=timezone.now()))

    def roaming(self):
        return self.filter(bolt_on__offering__bolt_on__roaming=True).exclude(bolt_on__offering__bolt_on__roaming_zone='e')

    def eu_roaming(self):
        return self.filter(bolt_on__offering__bolt_on__roaming=True, bolt_on__offering__bolt_on__roaming_zone='e')


class BoltOnPurchase(models.Model):
    bolt_on = models.ForeignKey(ClientBoltOn, on_delete=models.CASCADE)
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE, related_name='bolt_on_purchases')
    purchase_datetime = models.DateTimeField(default=timezone.now)
    price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    quantity = models.IntegerField(default=1)
    redeemed_via_perk = models.ForeignKey(PerkRedemption, on_delete=models.SET_NULL, null=True, blank=True)
    country_code = models.CharField(max_length=2, blank=True, null=True)
    expiry = models.DateTimeField(blank=True, null=True)

    objects = models.Manager.from_queryset(BoltOnPurchaseQuerySet)()

    @property
    def bolt_on_type(self):
        if self.bolt_on.offering.bolt_on.roaming:
            return 'roaming'
        if self.bolt_on.offering.data_gb:
            return 'data'
        if self.bolt_on.offering.voice_minutes:
            return 'voice'
        if self.bolt_on.offering.sms:
            return 'sms'
        return 'bolt-on'

    @property
    def expires(self):
        return self.bolt_on.get_expiry_date(self.purchase_datetime)

    @property
    def is_active(self):
        return self.expires >= timezone.now()

    @property
    def data_limit_gb(self):
        return self.bolt_on.data_gb

    @property
    def used_data_gb(self):
        return 0  # TOD0  # pylint: disable=fixme

    @property
    def data_gb_remaining(self):
        return self.data_limit_gb - self.used_data_gb

    @property
    def voice_limit_minutes(self):
        return self.bolt_on.voice_minutes

    @property
    def used_voice_minutes(self):
        return 0  # TOD0  # pylint: disable=fixme

    @property
    def voice_minutes_remaining(self):
        return self.voice_limit_minutes - self.used_voice_minutes

    @property
    def sms_limit(self):
        return self.bolt_on.sms

    @property
    def used_sms(self):
        return 0 # TODO  # pylint: disable=fixme

    @property
    def sms_remaining(self):
        return self.sms_limit - self.used_sms

    @property
    def roaming_zone(self):
        return self.bolt_on.roaming_zone

    def __str__(self):
        return f"{self.bolt_on} for {self.subscription}"


class RoamingEsimPackage(models.Model):
    name = models.CharField(max_length=255)
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='roaming_esim_packages')
    cost = models.DecimalField(max_digits=10, decimal_places=2, help_text='Cost to us')
    data_limit_gb = models.DecimalField(max_digits=5, decimal_places=2, default=0, help_text='Data limit in GB, 0 means unlimited')
    price = models.DecimalField(max_digits=6, decimal_places=2)
    days = models.PositiveIntegerField(default=1, help_text='Number of days the package is valid for')
    provider_code = models.CharField(max_length=50, blank=True, null=True)
    enabled = models.BooleanField(default=False)
    billing_product_id = models.CharField(max_length=50, blank=True, null=True)
    sim_batch = models.ForeignKey('SimBatch', on_delete=models.CASCADE, blank=True, null=True, related_name='roaming_esim_packages', help_text='Batch of SIMs (e.g. Nordic profile type. If null then non-batch esims will be used')

    @property
    def billing_price_id(self):
        if not self.client.plans_assigned_externally:
            if self.client.has_payment_integration and self.client.payment_integration.gateway == PaymentIntegration.Gateway.STRIPE:  # pylint: disable=no-member
                stripe_client = get_stripe_client(self.client.payment_integration.secret_credentials)  # pylint: disable=no-member
                product = stripe_client.products.retrieve(self.billing_product_id)
                return product.default_price
        return None

    def sync_to_gateway_if_necessary(self):
        if not self.client.plans_assigned_externally:
            if self.client.has_payment_integration and self.client.payment_integration.gateway == PaymentIntegration.Gateway.STRIPE:  # pylint: disable=no-member
                stripe_client = get_stripe_client(self.client.payment_integration.secret_credentials)  # pylint: disable=no-member
                if self.billing_product_id:
                    product = stripe_client.products.retrieve(self.billing_product_id)
                    stripe_client.products.update(product.id, params={'name': self.name})
                    price_id = product.default_price
                    price = stripe_client.prices.retrieve(price_id)
                    if self.price != price.unit_amount / 100:
                        new_price = stripe_client.prices.create(params={'product': product.id, 'unit_amount': int(self.price * 100), 'currency': 'gbp'})
                        stripe_client.products.update(product.id, params={'default_price': new_price.id})
                    self.save()
                else:
                    product = stripe_client.products.create(params={'name': self.name})
                    price = stripe_client.prices.create(params={'product': product.id, 'unit_amount': int(self.price * 100), 'currency': 'gbp'})
                    stripe_client.products.update(product.id, params={'default_price': price.id})
                    self.billing_product_id = product.id
                    self.save()


    def create_or_extend_roaming_esim(self, subscriber, extend_subscription_id, correlation_id):
        if extend_subscription_id:
            subscription = subscriber.subscriptions.filter(pk=extend_subscription_id).first()
        else:
            subscription = None
        provider_reference = None
        if subscription:
            provider_reference = subscription.try_extend_roaming_esim(self.provider_code)
            if not subscription.client.provider.is_demo:
                send_slack_message(f'Roaming esim extended ({subscription.client.name}, {subscription.subscriber.email}): [link in nexus]({{site_url}}/admin/core/subscription/{subscription.pk}/change/)')
        else:
            faked_billing_id = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
            subscription = subscriber.subscriptions.create(
                billing_subscription_id=f'roaming-{self.pk}-{faked_billing_id}',  # no recurring billing for roaming
                subscription_type=Subscription.SubscriptionTypes.ROAMING_ESIM,
                status=Subscription.Statuses.ACTIVE,
                correlation_id=correlation_id,
                start_date=timezone.now())
            provider_reference = subscription.try_activate_new_roaming_esim(self.sim_batch, self.provider_code)
            if not subscription.client.provider.is_demo:
                send_slack_message(f'New roaming esim created ({subscription.client.name}, {subscription.subscriber.email}): [link in nexus]({{site_url}}/admin/core/subscription/{subscription.pk}/change/)')
        return subscription, provider_reference

    def _get_extendable_roaming_subscription(self, subscriber, key_name):
        purchase = RoamingEsimPurchase.objects.filter(key_name=key_name, subscription__in=subscriber.subscriptions.all()).first()
        if purchase:
            return purchase.subscription
        return None


    def __str__(self):
        return f"{self.name} - {self.data_limit_gb}GB - ${self.price} ({self.days} days)"

    @property
    def is_unlimited(self):
        return self.data_limit_gb == 0

class RoamingEsimRegion(models.Model):
    name = models.CharField(max_length=255, unique=True)
    packages = models.ManyToManyField(RoamingEsimPackage, related_name="regions")

    def __str__(self):
        return self.name

class RoamingEsimCountry(models.Model):
    country_code = models.CharField(max_length=2, help_text='2-char ISO code')
    package = models.ForeignKey(RoamingEsimPackage, on_delete=models.CASCADE, related_name="countries")
    popularity = models.IntegerField(default=0, help_text="Higher means more popular")

    def __str__(self):
        return f"{self.country_code} - {self.package.name}"


class RoamingEsimPurchase(models.Model):
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE, related_name='roaming_esim_purchases')
    package = models.ForeignKey(RoamingEsimPackage, on_delete=models.CASCADE, related_name='purchases')
    purchase_datetime = models.DateTimeField(default=timezone.now)
    key_name = models.CharField(max_length=255, blank=True, null=True)
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    provider_reference = models.CharField(max_length=100, default='', blank=True, help_text='e.g. Telna package id')

    @property
    def expiry_datetime(self):
        return self.purchase_datetime + timedelta(days=self.package.days)

    @property
    def is_active(self):
        return timezone.now() <= self.expiry_datetime


class RoamingEsimSnapshot:
    def __init__(self, subscription, roaming_esim_purchases):
        self.subscription = subscription
        self.roaming_esim_purchases = roaming_esim_purchases
        self.first_purchase = self.roaming_esim_purchases[0]
        usage = 0
        limit = 0
        for purchase in self.roaming_esim_purchases:
            limit += gb_to_bytes(purchase.package.data_limit_gb)
        if self.subscription.subscriber.client.roaming_esim_provider.name.lower() == 'telna':
            for purchase in self.roaming_esim_purchases:
                specific_usage, _ = TelnaClient().get_data_usage_and_allowance(purchase.provider_reference)
                usage += specific_usage
        else:
            usage = 0
        self.data_used_gb = bytes_to_gb(usage)
        self.data_left_gb = bytes_to_gb(limit - usage)

    @staticmethod
    def from_subscription(subscription):
        purchases = list(RoamingEsimPurchase.objects.filter(subscription=subscription))
        if purchases:
            return RoamingEsimSnapshot(subscription, purchases)
        return None

    @property
    def correlation_id(self):
        return self.subscription.correlation_id

    @property
    def is_active(self):
        return self.subscription.is_active and timezone.now() <= self.expiry_datetime

    @property
    def zone_name_or_country_code(self):
        return self.first_purchase.key_name

    @property
    def expiry_iso_datestring(self):
        return self.expiry_datetime.isoformat()

    @property
    def expiry_datetime(self):
        return max((purchase.expiry_datetime for purchase in self.roaming_esim_purchases))

    @property
    def id(self):
        return self.subscription.id

    @property
    def custom_name(self):
        return self.subscription.user_subscription_name

    @property
    def is_unlimited(self):
        return any((purchase.is_active and purchase.package.is_unlimited for purchase in self.roaming_esim_purchases))

    @property
    def is_first_esim(self):
        sim = self.subscription.latest_sim
        if sim:
            return sim.subscriptions.count() == 1 and self.subscription.roaming_esim_purchases.count() == 1
        return False

class PurchaseIntent(models.Model):
    class PurchaseType(models.TextChoices):
        PLAN_SIGNUP = 'plan_signup', 'Plan Signup'
        BASKET_CHECKOUT = 'basket_checkout', 'Basket Checkout'
        BOLT_ON = 'bolt_on', 'Bolt-on'
        ROAMING_PACKAGE = 'roaming_package', 'Roaming Package'
        CARD_UPDATE = 'card_update', 'Card Update'

    class Status(models.TextChoices):
        INITIATED = 'initiated', 'Initiated'
        CHECKOUT_CREATED = 'checkout_created', 'Checkout Created'
        PAYMENT_COMPLETED = 'payment_completed', 'Payment Completed'
        FULFILLED = 'fulfilled', 'Fulfilled'
        FAILED = 'failed', 'Failed'
        CANCELLED = 'cancelled', 'Cancelled'

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    purchase_type = models.CharField(max_length=20, choices=PurchaseType.choices)
    status = models.CharField(max_length=20, choices=Status.choices, default=Status.INITIATED)

    subscriber = models.ForeignKey('Subscriber', on_delete=models.CASCADE, related_name='purchase_intents')
    client = models.ForeignKey('Client', on_delete=models.CASCADE, related_name='purchase_intents')

    stripe_session_id = models.CharField(max_length=200, blank=True, null=True, db_index=True)
    stripe_payment_intent_id = models.CharField(max_length=200, blank=True, null=True)

    purchase_data = models.JSONField(
        help_text="Purchase-specific data like bolt_on_id, plan_id, price, etc."
    )

    is_dry_run = models.BooleanField(
        default=True,
        help_text="If True, logs what would happen without actually fulfilling"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    error_message = models.TextField(blank=True, null=True)
    retry_count = models.IntegerField(default=0)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['stripe_session_id']),
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['subscriber', 'status']),
        ]

    def __str__(self):
        return f"{self.get_purchase_type_display()} - {self.subscriber.email} ({self.get_status_display()})"

    @property
    def is_completed(self):
        return self.status in [self.Status.FULFILLED, self.Status.FAILED, self.Status.CANCELLED]

    def mark_checkout_created(self, stripe_session_id):
        self.stripe_session_id = stripe_session_id
        self.status = self.Status.CHECKOUT_CREATED
        self.save(update_fields=['stripe_session_id', 'status', 'updated_at'])

    def mark_payment_completed(self, stripe_payment_intent_id=None):
        if stripe_payment_intent_id:
            self.stripe_payment_intent_id = stripe_payment_intent_id
        self.status = self.Status.PAYMENT_COMPLETED
        self.save(update_fields=['stripe_payment_intent_id', 'status', 'updated_at'])

    def mark_fulfilled(self):
        self.status = self.Status.FULFILLED
        self.completed_at = timezone.now()
        self.save(update_fields=['status', 'completed_at', 'updated_at'])

    def handle_stripe_event(self, event_type, stripe_event):
        from core.purchase_intent import log_purchase_intent_action  # pylint: disable=import-outside-toplevel

        if event_type == 'checkout.session.completed':
            self._handle_checkout_session_completed(stripe_event)
        else:
            log_purchase_intent_action(
                self,
                f"UNHANDLED STRIPE EVENT: {event_type}",
                {'event_id': getattr(stripe_event, 'id', 'unknown')}
            )

    def _handle_checkout_session_completed(self, stripe_event):
        from core.purchase_intent import log_purchase_intent_action  # pylint: disable=import-outside-toplevel

        session = stripe_event.data.object

        log_purchase_intent_action(
            self,
            "STRIPE EVENT RECEIVED",
            {
                'event_type': 'checkout.session.completed',
                'session_id': session.id,
                'current_status': self.get_status_display(),
                'amount_total': getattr(session, 'amount_total', None)
            }
        )

        if self.status == self.Status.CHECKOUT_CREATED:
            self.mark_payment_completed()
            log_purchase_intent_action(
                self,
                "PAYMENT COMPLETED VIA CHECKOUT SESSION",
                {
                    'trigger': 'checkout.session.completed',
                    'session_id': session.id,
                    'purchase_type': self.get_purchase_type_display(),
                    'is_dry_run': self.is_dry_run
                }
            )
        elif self.status == self.Status.PAYMENT_COMPLETED:
            log_purchase_intent_action(
                self,
                "DUPLICATE CHECKOUT SESSION COMPLETED",
                {
                    'session_id': session.id,
                    'note': 'Payment already completed, ignoring duplicate event'
                }
            )
        else:
            log_purchase_intent_action(
                self,
                "UNEXPECTED CHECKOUT SESSION COMPLETED",
                {
                    'session_id': session.id,
                    'current_status': self.get_status_display(),
                    'note': f'Received checkout.session.completed in {self.get_status_display()} state'
                }
            )

    def mark_failed(self, error_message=None):
        self.status = self.Status.FAILED
        if error_message:
            self.error_message = error_message
        self.completed_at = timezone.now()
        self.save(update_fields=['status', 'error_message', 'completed_at', 'updated_at'])

class DeliverooRiderData(models.Model):
    lookup_key = models.CharField(max_length=255, db_index=True, help_text='Hashed key for rider validation (email+rider_id combination)')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='deliveroo_rider_data')

    class Meta:
        verbose_name = 'Deliveroo Rider Data'
        verbose_name_plural = 'Deliveroo Rider Data'
        unique_together = [['client', 'lookup_key']]

    def __str__(self):
        return f"Deliveroo rider data for {self.client.name} - {self.lookup_key[:10]}..."
