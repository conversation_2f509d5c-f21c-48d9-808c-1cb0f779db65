from datetime import date
import hashlib
import json
import jwt
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.exceptions import AuthenticationFailed
from rest_framework.permissions import BasePermission
from jwcrypto.jwk import JWK
from django.core.exceptions import PermissionDenied
from django.conf import settings
from core.cognito import cognito_key_cache
from core.models import Subscriber, DeliverooRiderData, Client
from .nexus_interface import NexusInterface
from .subscriber_auth import verify_our_custom_jwt_for_openid, get_username_from_jwt, get_auth_client_id_from_jwt
from .serializers import ClientSerializer, PlanSerializer, SubscriberSerializer, PlanChangeSerializer, BoltOnSerializer,ESimSettingsSerializer, RoamingBoltOnSerializer, RoamingSimSerializer, SubscriberReferralSerializer


def get_client_from_username(cognito_username):
    user = Subscriber.objects.filter(cognito_username=cognito_username).first()
    if user:
        return user.client
    return None


def jwt_is_valid(jwt_token):
    username = get_username_from_jwt(jwt_token)
    client = get_client_from_username(username)
    if client:
        if client.is_openid_subscriber_auth:
            verify_our_custom_jwt_for_openid(jwt_token, client)
        else:
            client_id = client.subscriber_user_pool_client_id
            pool_id = client.subscriber_user_pool_id
            if get_auth_client_id_from_jwt(jwt_token) != client_id:
                if username.startswith('google'):
                    client_id = settings.GOOGLE_AUTH_CLIENT_ID
                    pool_id = settings.GOOGLE_AUTH_POOL_ID
                elif username.startswith('signinwithapple'):
                    client_id = settings.APPLE_AUTH_CLIENT_ID
                    pool_id = settings.APPLE_AUTH_POOL_ID
            verify_jwt(jwt_token, client_id, f'https://cognito-idp.eu-west-2.amazonaws.com/{pool_id}', 'access', pool_id)
        return True
    return False

def verify_jwt(jwt_token, audience, issuer, token_use, user_pool_id):
    headers = jwt.get_unverified_header(jwt_token)
    jwk = cognito_key_cache.get_matching_signing_key(user_pool_id, headers['kid'])
    key = JWK.from_json(json.dumps(jwk)).export_to_pem()
    try:
        decoded_token = jwt.decode(jwt_token, key, algorithms=['RS256'], issuer=issuer, leeway=60)
    except jwt.exceptions.ExpiredSignatureError:
        raise PermissionDenied('Token has expired')  # pylint: disable=raise-missing-from
    if decoded_token['token_use'] != token_use:
        raise RuntimeError('Invalid token use')
    if decoded_token['client_id'] != audience:
        raise RuntimeError('Invalid client id')


class BearerTokenAuthentication(BasePermission):
    def has_permission(self, request, _):
        auth_header = request.headers.get('Authorization')

        if not auth_header:
            return False

        if not auth_header.startswith('Bearer '):
            raise AuthenticationFailed('Invalid authentication header format')

        token = auth_header.split(' ')[1]
        if not jwt_is_valid(token):
            raise AuthenticationFailed('Invalid token')

        request.access_token = token
        request.cognito_username = get_username_from_jwt(token)
        return True


class BearerTokenOptional(BasePermission):
    def has_permission(self, request, _):
        auth_header = request.headers.get('Authorization')

        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
            if jwt_is_valid(token):
                request.access_token = token
                request.cognito_username = get_username_from_jwt(token)
        return True


class LoginView(APIView):
    def post(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        email = request.data.get('email')
        password = request.data.get('password')
        token_dict = nexus_interface.login_user(email, password)

        if token_dict:
            return Response({
                'access_token': token_dict['AccessToken'],
                'id_token': token_dict['IdToken'],
                'refresh_token': token_dict['RefreshToken']
            }, status=status.HTTP_200_OK)
        else:
            return Response({'error': 'Invalid username or password'}, status=status.HTTP_401_UNAUTHORIZED)


class ForgotPasswordView(APIView):
    def post(self, request, *, client_id):
        email = request.data.get('email')

        if not email:
            return Response({'error': 'Email is missing'}, status=status.HTTP_400_BAD_REQUEST)
        nexus_interface = NexusInterface(client_id)
        nexus_interface.forgot_password(email)
        return Response({}, status=status.HTTP_200_OK)


class ConfirmForgotPasswordView(APIView):
    def post(self, request, *, client_id):
        email = request.data.get('email')
        verification_code = request.data.get('verification_code')
        new_password = request.data.get('new_password')

        if not all([email, verification_code, new_password]):
            return Response({'error': 'Required parameters are missing'}, status=status.HTTP_400_BAD_REQUEST)

        nexus_interface = NexusInterface(client_id)
        response = nexus_interface.confirm_forgot_password(email, verification_code, new_password)
        if response == 'ok':
            return Response({}, status=status.HTTP_200_OK)
        else:
            msg = {
                'invalid-password': 'Password must be at least 8 characters long, contain at least one uppercase letter, one lowercase letter, one number and one special character',
                'code-mismatch': 'Failed to reset password - invalid code or code mismatch'
            }
            return Response({'error': msg.get(response, 'Failed to reset password - invalid code or code mismatch')}, status=status.HTTP_400_BAD_REQUEST)

class SignUpView(APIView):
    def post(self, request, *, client_id):
        email = request.data.get('email')
        password = request.data.get('password')
        # is_underage = request.data.get('is_underage')

        if not all([email, password]):
            return Response({'error': 'Required parameters are missing'}, status=status.HTTP_400_BAD_REQUEST)

        nexus_interface = NexusInterface(client_id)
        date_of_birth = request.data.get('date_of_birth')
        metadata = request.data.get('metadata')
        response = nexus_interface.sign_up_user(
            email,
            password,
            name=request.data.get('name'),
            date_of_birth=date.fromisoformat(date_of_birth) if date_of_birth else None,
            metadata=metadata
        )
        if isinstance(response, dict):
            return Response(response, status=status.HTTP_200_OK)
        else:
            msg = {
                'invalid-password': 'Password must be at least 8 characters long, contain at least one uppercase letter, one lowercase letter, one number and one special character',
                'username-exists': 'Failed to sign up - email already in use'
            }
            return Response({'error': msg.get(response, 'Failed to sign up')}, status=status.HTTP_400_BAD_REQUEST)

class ChangePasswordView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def post(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        old_password = request.data.get('old_password')
        new_password = request.data.get('new_password')
        response = nexus_interface.change_user_password(request.access_token, old_password, new_password)
        if response == 'ok':
            nexus_interface.send_password_changed_email(request.cognito_username)
            return Response({'success': 'Password changed successfully'})
        else:
            msg = {
                'no-match': 'Incorrect old password',
                'invalid-password': 'Password must be at least 8 characters long, contain at least one uppercase letter, one lowercase letter, one number and one special character',
                'limit-exceeded': 'Failed to change password - limit exceeded, please try again in a few minutes'
            }
            return Response({'error': msg.get(response, 'Not authorised')}, status=status.HTTP_400_BAD_REQUEST)

class RefreshTokenView(APIView):
    def post(self, request, *, client_id):
        refresh_token = request.data.get('refresh_token')
        cognito_username = request.data.get('cognito_username')
        if not refresh_token:
            return Response({'error': 'Refresh token is missing'}, status=status.HTTP_400_BAD_REQUEST)

        nexus_interface = NexusInterface(client_id)
        response = nexus_interface.refresh_token(cognito_username, refresh_token)

        if response:
            return Response(response, status=status.HTTP_200_OK)
        else:
            return Response({'error': 'Failed to refresh token - need a new login'}, status=status.HTTP_400_BAD_REQUEST)


class ClientDetailView(APIView):
    permission_classes = [BearerTokenOptional]

    def get(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        client_data = nexus_interface.get_client_data()
        if not client_data:
            return Response(status=status.HTTP_404_NOT_FOUND)

        plans_data = nexus_interface.get_all_plans(getattr(request, 'cognito_username', None))

        client_serializer = ClientSerializer(client_data)
        plans_serializer = PlanSerializer(plans_data, many=True)

        client_data['plans'] = plans_serializer.data
        response_data = client_serializer.data

        return Response(response_data)


class ClientPlansView(APIView):
    permission_classes = [BearerTokenOptional]

    def get(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        plans = nexus_interface.get_all_plans(getattr(request, 'cognito_username', None))
        if not plans:
            return Response(status=status.HTTP_404_NOT_FOUND)
        serializer = PlanSerializer(plans, many=True)
        return Response(serializer.data)


class SubscriberDetailView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def get(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        subscriber_data = nexus_interface.get_subscriber_data(request.cognito_username)
        if not subscriber_data:
            return Response(status=status.HTTP_404_NOT_FOUND)

        subscriber_serializer = SubscriberSerializer(subscriber_data)
        response_data = subscriber_serializer.data

        return Response(response_data)

    def put(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        subscription_id = request.data.get('subscription_id')
        address = request.data.get('address')
        if address:
            nexus_interface.update_address(request.cognito_username, address)
        date_of_birth = request.data.get('date_of_birth')
        name = request.data.get('name', '')
        if date_of_birth or name:
            nexus_interface.update_subscriber_details(request.cognito_username, date.fromisoformat(date_of_birth) if date_of_birth else None, name)
        send_marketing = request.data.get('send_marketing', None)
        if send_marketing is not None:
            nexus_interface.update_marketing_subscription(request.cognito_username, send_marketing)
        sim_type = request.data.get('sim_type')
        if sim_type is not None:
            nexus_interface.update_sim_type(request.cognito_username, sim_type, subscription_id)
        plan_name = request.data.get('plan_name')
        if plan_name:
            nexus_interface.update_plan_name(request.cognito_username, plan_name, subscription_id)
        roaming_sim_name = request.data.get('roaming_sim_name')
        if roaming_sim_name:
            nexus_interface.update_roaming_sim_name(request.cognito_username, roaming_sim_name, subscription_id)


        return self.get(request, client_id=client_id)

class CancelSubscriptionView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def post(self, request, *, client_id):
        subscription_id = request.data.get('subscription_id')
        nexus_interface = NexusInterface(client_id)
        nexus_interface.cancel_subscription(request.cognito_username, subscription_id)
        return Response({'end_date': nexus_interface.get_subscription_end_date(request.cognito_username)}, status=status.HTTP_200_OK)


class PACRequestAPIView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def post(self, request, *, client_id):
        pac_code = request.data.get('pac_code')
        phone_number = request.data.get('phone_number')
        subscription_id = request.data.get('subscription_id')
        desired_date = date.fromisoformat(request.data.get('desired_date')) if request.data.get('desired_date') else None
        if pac_code is None or phone_number is None:
            return Response({"error": "Both PAC code and phone number are required"}, status=status.HTTP_400_BAD_REQUEST)
        nexus_interface = NexusInterface(client_id)
        nexus_interface.request_pac_code(pac_code, phone_number, desired_date, request.cognito_username, subscription_id)
        subscriber_change = nexus_interface.hide_notification_dialog(request.cognito_username, "number_transfer", subscription_id)
        subscriber_change_serializer = SubscriberSerializer(subscriber_change)
        return Response(subscriber_change_serializer.data, status=status.HTTP_201_CREATED)


class ActivateSimView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def post(self, request, *, client_id):
        code = request.data.get('code')
        subscription_id = request.data.get('subscription_id')
        nexus_interface = NexusInterface(client_id)
        valid = nexus_interface.try_activate_sim(code, request.cognito_username, subscription_id)
        if valid:
            return Response({}, status=status.HTTP_200_OK)
        else:
            return Response({"error": "SIM Serial not correct. Please try again."}, status=status.HTTP_400_BAD_REQUEST)


class VerifyView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def post(self, request, *, client_id):
        code = request.data.get('code')
        nexus_interface = NexusInterface(client_id)
        response = nexus_interface.verify_user(request.cognito_username, code)
        if response == 'ok':
            return Response({}, status=status.HTTP_200_OK)
        else:
            return Response({'error': response}, status=status.HTTP_400_BAD_REQUEST)

class ResendVerificationCodeView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def post(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        nexus_interface.resend_verification_email(request.cognito_username)
        return Response({}, status=status.HTTP_200_OK)

class SendESimInstructionsView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def post(self, request, *, client_id):
        email = request.data.get('email')
        nexus_interface = NexusInterface(client_id)
        nexus_interface.send_esim_instructions(request.cognito_username, email)
        return Response({}, status=status.HTTP_200_OK)


class PlanChangeView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def post(self, request, *, client_id):
        plan_id = request.data.get('target_plan_id')
        subscription_id = request.data.get('subscription_id')
        change_type = request.data.get('change_type')
        nexus_interface = NexusInterface(client_id)
        if change_type == 'upgrade':
            nexus_interface.upgrade_plan(request.cognito_username, plan_id, subscription_id)
        elif change_type == 'downgrade':
            nexus_interface.downgrade_plan(request.cognito_username, plan_id, subscription_id)
        elif change_type == 'cancel':
            nexus_interface.cancel_subscription(request.cognito_username, subscription_id)
        return Response({}, status=status.HTTP_201_CREATED)

    def get(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        plan_change_serializer = PlanChangeSerializer(nexus_interface.get_plan_changes(request.cognito_username), many=True)
        response_data = plan_change_serializer.data
        return Response(response_data)


class PlanChangeDetailView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def get(self, request, *, client_id, plan_change_id):
        nexus_interface = NexusInterface(client_id)
        plan_change = nexus_interface.get_plan_change(request.cognito_username, plan_change_id)
        if not plan_change:
            return Response(status=status.HTTP_404_NOT_FOUND)
        plan_change_serializer = PlanChangeSerializer(plan_change)
        return Response(plan_change_serializer.data)


class CancelPlanChangeView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def post(self, request, *, client_id, plan_change_id):
        subscription_id = request.data.get('subscription_id')
        nexus_interface = NexusInterface(client_id)
        nexus_interface.cancel_plan_change(request.cognito_username, plan_change_id, subscription_id)
        return Response({}, status=status.HTTP_200_OK)

class HideNotificationDialogView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def post(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        dialog_type = request.data.get('dialog_type')
        subscription_id = request.data.get('subscription_id')
        subscriber_change = nexus_interface.hide_notification_dialog(request.cognito_username, dialog_type, subscription_id)
        if not subscriber_change:
            return Response(status=status.HTTP_404_NOT_FOUND)

        subscriber_change_serializer = SubscriberSerializer(subscriber_change)
        return Response(subscriber_change_serializer.data, status=status.HTTP_200_OK)

class ClientPerksView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def get(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        perks = nexus_interface.get_all_perks(request.cognito_username)
        data = {
            'perks': perks,
            'balance': nexus_interface.get_perk_point_balance(request.cognito_username)
        }
        return Response(data)

class ClaimPerkView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def post(self, request, *, client_id):
        perk_id = request.data.get('id')
        claim_with_points = str(request.data.get('claim_with_points')).lower() == 'true'

        nexus_interface = NexusInterface(client_id)
        response = nexus_interface.claim_perk(request.cognito_username, perk_id, claim_with_points)
        if response.get('success'):
            perks = nexus_interface.get_all_perks(request.cognito_username)
            data = {
                'perks': perks,
                'balance': nexus_interface.get_perk_point_balance(request.cognito_username),
                'success': True
            }
            return Response(data, status=status.HTTP_200_OK)
        elif response.get('error'):
            return Response({'error': response['error']}, status=status.HTTP_400_BAD_REQUEST)
        return Response({'error': 'Unknown error occurred'}, status=status.HTTP_400_BAD_REQUEST)

class BoltOnsView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def get(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        bolt_ons = nexus_interface.get_all_bolt_ons()

        serializer = BoltOnSerializer(bolt_ons, many=True)
        return Response(serializer.data)


class RoamingBoltOnsView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def get(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        bolt_ons = nexus_interface.get_roaming_bolt_ons()

        serializer = RoamingBoltOnSerializer(bolt_ons, many=True)
        return Response(serializer.data)


class ESimSettingsPlanView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def get(self, request, *, client_id, plan_id):
        nexus_interface = NexusInterface(client_id)
        esim_settings = nexus_interface.get_e_sim_settings(request.cognito_username, plan_id)

        serializer = ESimSettingsSerializer(esim_settings)
        return Response(serializer.data)

class ESimSettingsView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def get(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        esim_settings = nexus_interface.get_e_sim_settings(request.cognito_username, None)

        serializer = ESimSettingsSerializer(esim_settings)
        return Response(serializer.data)

class AuthSsoView(APIView):
    def post(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        code = request.data.get('code')
        return Response(nexus_interface.login_via_cognito_sso(code), status=status.HTTP_200_OK)

class ApplyReferralView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def post(self, request, *, client_id):
        code = request.data.get('code')

        nexus_interface = NexusInterface(client_id)
        subscriber_change = nexus_interface.apply_referral(request.cognito_username, code)
        if not subscriber_change:
            return Response({'error': 'This is not a valid referral code.'}, status=status.HTTP_404_NOT_FOUND)

        subscriber_change_serializer = SubscriberSerializer(subscriber_change)

        return Response(subscriber_change_serializer.data, status=status.HTTP_200_OK)


class OpenIDLoginView(APIView):
    def get(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        login_data = nexus_interface.get_openid_login_data()
        if login_data:
            return Response(login_data, status=status.HTTP_200_OK)
        return Response(status=status.HTTP_404_NOT_FOUND)


class LoginViaOpenIDView(APIView):
    def post(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        code = request.data.get('code')
        state = request.data.get('state')
        state_plaintext = request.data.get('state_plaintext')
        code_verifier = request.data.get('code_verifier')
        response = nexus_interface.login_via_openid(code, state, state_plaintext, code_verifier)
        if response:
            return Response(response, status=status.HTTP_200_OK)
        return Response({'error': 'Failed to login'}, status=status.HTTP_400_BAD_REQUEST)


class TestAuthView(APIView):
    def get(self, request, *args, **kwargs):
        auth_header = request.headers.get('Authorization')
        res = {'success': False}

        if auth_header and auth_header.startswith('Bearer '):
            jwt_token = auth_header.split(' ')[1]
            username = get_username_from_jwt(jwt_token)
            client = get_client_from_username(username)
            if client:
                res = {'success': True}
        return Response(res, status=status.HTTP_200_OK)


class ReferralAPIView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def get(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        referral_data = nexus_interface.get_referral_data(request.cognito_username)
        if not referral_data:
            return Response(status=status.HTTP_404_NOT_FOUND)

        serializer = SubscriberReferralSerializer(referral_data)
        return Response(serializer.data)


class RoamingSimsView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def get(self, request, *, client_id):
        nexus_interface = NexusInterface(client_id)
        roaming_package_data = nexus_interface.get_roaming_package_data()
        if not roaming_package_data:
            return Response(status=status.HTTP_404_NOT_FOUND)

        serializer = RoamingSimSerializer(roaming_package_data)
        return Response(serializer.data)


class ShareEsimView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def post(self, request, *, client_id, subscription_id=None):
        nexus_interface = NexusInterface(client_id)
        nexus_interface.share_esim_instructions(request.cognito_username, request.data.get('email'), subscription_id)
        return Response(status=status.HTTP_200_OK)

class ConfirmPerkRedemptionView(APIView):
    permission_classes = [BearerTokenAuthentication]

    def post(self, request, *, client_id, redemption_id):
        nexus_interface = NexusInterface(client_id)
        response = nexus_interface.confirm_perk_redemption(request.cognito_username, redemption_id)
        if response.get('success'):
            perks = nexus_interface.get_all_perks(request.cognito_username)
            data = {
                'perks': perks,
                'balance': nexus_interface.get_perk_point_balance(request.cognito_username),
                'success': True
            }
            return Response(data, status=status.HTTP_200_OK)
        elif response.get('error'):
            return Response({'error': response['error']}, status=status.HTTP_400_BAD_REQUEST)
        return Response({'error': 'Unknown error occurred'}, status=status.HTTP_400_BAD_REQUEST)

class DeliverooValidateRiderView(APIView):
    def post(self, request, *, client_id):
        email = request.data.get('email')
        rider_id = request.data.get('rider_id')

        if not all([email, rider_id]):
            return Response({'error': 'Both email and rider_id are required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            client = Client.objects.get(id=client_id)
            lookup_data = f"{email.lower()}:{rider_id}"
            lookup_key = hashlib.sha256(lookup_data.encode('utf-8')).hexdigest()
            rider_data_exists = DeliverooRiderData.objects.filter(
                client=client,
                lookup_key=lookup_key
            ).exists()

            return Response({'valid': True}, status=status.HTTP_200_OK)

        except Exception as e:  # pylint: disable=broad-exception-caught
            return Response({'error': 'Validation failed', 'details': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DeliverooUploadRiderDataView(APIView):
    def post(self, request, *, client_id):
        lookup_keys = request.data.get('lookup_keys', [])

        if not lookup_keys or not isinstance(lookup_keys, list):
            return Response({'error': 'lookup_keys must be a non-empty list'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            client = Client.objects.get(id=client_id)
            DeliverooRiderData.objects.filter(client=client).delete()
            rider_data_objects = [
                DeliverooRiderData(client=client, lookup_key=lookup_key)
                for lookup_key in lookup_keys
            ]

            created_count = len(DeliverooRiderData.objects.bulk_create(
                rider_data_objects,
                ignore_conflicts=True
            ))

            return Response({
                'success': True,
                'uploaded_count': created_count,
                'total_provided': len(lookup_keys)
            }, status=status.HTTP_200_OK)

        except Exception as e:  # pylint: disable=broad-exception-caught
            return Response({'error': 'Upload failed', 'details': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
