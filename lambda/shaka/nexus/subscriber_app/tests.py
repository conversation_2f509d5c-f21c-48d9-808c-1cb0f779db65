import unittest
from datetime import datetime, timedelta
import calendar
from contextlib import contextmanager
from decimal import Decimal
import hashlib
import inspect
import pytz
from dateutil.relativedelta import relativedelta
from rest_framework import status
from rest_framework.test import APIClient
from core.test_utils import NexusTestCase
from core.tests.factories import ClientFactory, SubscriptionFactory, PlanChangeFactory, PlanFactory, SubscriberFactory
from core.models import PlanChange, Referral, VoucherPerk, DeliverooRiderData, Subscriber, PurchaseIntent
from subscriber_app.views import BearerTokenAuthentication
from subscriber_app.nexus_interface import NexusInterface
from subscriber_app.billing_views import BasketCheckoutView
from next_api.models import FamilyPlanLink, TravelAddon, PlanTravelAddon


class MonkeyPatchSubscriberAuth:
    def __init__(self, subscriber):
        self.subscriber = subscriber
        self.old_has_permission = None

    def __enter__(self):
        self.old_has_permission = getattr(BearerTokenAuthentication, 'has_permission')
        setattr(BearerTokenAuthentication, 'has_permission', self.has_permission)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        setattr(BearerTokenAuthentication, 'has_permission', self.old_has_permission)

    def has_permission(self, request, _):
        request.access_token = ''
        request.cognito_username = self.subscriber.cognito_username
        return True


class SubscriberAppTestCase(NexusTestCase):
    def setUp(self):
        super().setUp()
        self.client = ClientFactory()
        self.subscriber = SubscriberFactory(client=self.client)

    @contextmanager
    def forced_auth_subscriber(self):
        with MonkeyPatchSubscriberAuth(self.subscriber):
            yield

    def get(self, path):
        client = APIClient()
        client.headers = {'Content-Type': 'application/json'}
        with self.forced_auth_subscriber():
            response = client.get(f'/s/api/v1/{self.client.obfuscated_id}{path}')
        return response

    def post(self, path, data):
        client = APIClient()
        client.headers = {'Content-Type': 'application/json'}
        with self.forced_auth_subscriber():
            response = client.post(f'/s/api/v1/{self.client.obfuscated_id}{path}', data, format='json')
        return response

class SubscribedSubscriberAppTestCase(SubscriberAppTestCase):
    def setUp(self):
        super().setUp()
        self.subscription = SubscriptionFactory(using=True, subscriber=self.subscriber)


class SubscriberDetailTests(SubscribedSubscriberAppTestCase):
    stripe_backend = 'fake'

    def setUp(self):
        super().setUp()
        self.scenario.setup_billing_for_client(self.client)

    def test_subscriber_detail(self):
        response = self.get('/data/subscriber/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        assert response.json()['plans']

    def test_next_bill_date_epoch(self):
        response = self.get('/data/subscriber/')
        uk_tz = pytz.timezone('Europe/London')
        start_of_next_month = self.fake_services.stripe.current_time.astimezone(uk_tz).replace(day=1, hour=0, minute=0, second=0, microsecond=0) + relativedelta(months=1)
        assert response.json()['plans'][0]['next_bill_date_epoch'] == int(start_of_next_month.timestamp())

def is_near_end_of_month():
    now = datetime.utcnow()
    last_day = calendar.monthrange(now.year, now.month)[1]
    end_of_month = datetime(now.year, now.month, last_day, 23, 59, 59)
    return now >= (end_of_month - timedelta(hours=4))

@unittest.skipIf(is_near_end_of_month(), "Skipping tests near the end of the month.")  # I'm sorry
class PlanChangeTests(SubscribedSubscriberAppTestCase):
    def get_plan_from_detail(self):
        response = self.get('/data/subscriber/')
        data = response.json()
        assert len(data['plans']) == 1
        plan = data['plans'][0]
        return plan

    def test_subscriber_detail_handles_no_plan_change(self):
        plan = self.get_plan_from_detail()
        assert plan['can_upgrade']
        assert plan['can_downgrade']
        assert plan['can_cancel']
        assert not plan['can_cancel_change']
        assert plan['plan_changes'] == []

    def test_subscriber_detail_handles_upgrade(self):
        plan_change = PlanChangeFactory(upgrade=True, subscription=self.subscription)
        plan = self.get_plan_from_detail()
        assert not plan['can_upgrade']
        assert not plan['can_downgrade']
        assert not plan['can_cancel']
        assert not plan['can_cancel_change']
        assert plan['plan_changes'][0]['id'] == plan_change.pk

    def test_subscriber_detail_handles_completed_upgrade(self):
        plan_change = PlanChangeFactory(upgrade=True, subscription=self.subscription, status=PlanChange.Status.COMPLETE)
        plan = self.get_plan_from_detail()
        assert plan['can_upgrade']
        assert plan['can_downgrade']
        assert plan['can_cancel']
        assert not plan['can_cancel_change']
        assert plan['plan_changes'][0]['id'] == plan_change.pk

    def test_subscriber_detail_can_cancel_downgrade(self):
        PlanChangeFactory(downgrade=True, subscription=self.subscription, status=PlanChange.Status.IN_PROGRESS)
        plan = self.get_plan_from_detail()
        assert not plan['can_upgrade']
        assert not plan['can_downgrade']
        assert not plan['can_cancel']
        assert plan['can_cancel_change']

    def test_subscriber_detail_handles_locked_downgrade(self):
        PlanChangeFactory(downgrade=True, subscription=self.subscription, status=PlanChange.Status.LOCKED)
        plan = self.get_plan_from_detail()
        assert not plan['can_upgrade']
        assert not plan['can_downgrade']
        assert not plan['can_cancel']
        assert not plan['can_cancel_change']

    def test_subscriber_detail_handles_cancel_in_progress(self):
        plan_change = PlanChangeFactory(downgrade=True, subscription=self.subscription, status=PlanChange.Status.IN_PROGRESS)
        PlanChangeFactory(cancel_change=True, subscription=self.subscription, status=PlanChange.Status.IN_PROGRESS, target_plan_change=plan_change)
        plan = self.get_plan_from_detail()
        assert not plan['can_upgrade']
        assert not plan['can_downgrade']
        assert not plan['can_cancel']
        assert not plan['can_cancel_change']

    def test_can_trigger_upgrade(self):
        plan = self.get_plan_from_detail()
        target_plan = PlanFactory(client=self.client, top_tier=True)
        assert plan['can_upgrade']
        response = self.post('/subscription/plan-change/', {'change_type': 'upgrade', 'target_plan_id': target_plan.id})
        assert response.status_code == status.HTTP_201_CREATED
        plan = self.get_plan_from_detail()
        assert not plan['can_upgrade']
        plan_change = plan['plan_changes'][0]
        assert plan_change['change_type'] == 'upgrade'
        assert plan_change['status'] == 'in_progress'

    def test_can_get_plan_change_detail(self):
        plan_change = PlanChangeFactory(upgrade=True, subscription=self.subscription)
        response = self.get(f'/subscription/plan-change/{plan_change.pk}/')
        assert response.status_code == status.HTTP_200_OK
        assert response.json()['id'] == plan_change.pk

    def test_can_get_plan_change_list(self):
        plan_change = PlanChangeFactory(upgrade=True, subscription=self.subscription)
        response = self.get('/subscription/plan-change/')
        assert response.status_code == status.HTTP_200_OK
        assert response.json()[0]['id'] == plan_change.pk


class PlanSignUpTests(SubscriberAppTestCase):
    stripe_backend = 'fake'

    def setUp(self):
        super().setUp()
        self.scenario.setup_billing_for_client(self.client)

    def test_billing_anchor_is_set_correctly(self):
        plan = PlanFactory(client=self.client)
        response = self.post(f'/plans/sign-up/{plan.pk}/', {})
        assert response.status_code == status.HTTP_200_OK
        session = self.fake_services.stripe.get_session(response.json()['session_id'])
        uk_tz = pytz.timezone('Europe/London')
        start_of_next_month = self.fake_services.stripe.current_time.astimezone(uk_tz).replace(day=1, hour=0, minute=0, second=0, microsecond=0) + relativedelta(months=1)
        assert session['subscription_data']['billing_cycle_anchor'] == int(start_of_next_month.timestamp())

    def test_subscription_session_metadata_includes_session_purpose(self):
        plan = PlanFactory(client=self.client)
        response = self.post(f'/plans/sign-up/{plan.pk}/', {})
        assert response.status_code == status.HTTP_200_OK
        session = self.fake_services.stripe.get_session(response.json()['session_id'])

        assert 'subscription_data' in session
        assert 'metadata' in session['subscription_data']
        assert session['subscription_data']['metadata']['session_purpose'] == 'subscription'

        assert 'plan_id' in session['subscription_data']['metadata']
        assert 'correlation_id' in session['subscription_data']['metadata']

    def test_card_update_session_metadata_includes_session_purpose(self):
        subscription = SubscriptionFactory(subscriber=self.subscriber, billing_subscription_id='test_sub_123')

        response = self.post('/subscription/change-card/', {'subscription_id': subscription.pk})
        assert response.status_code == status.HTTP_200_OK
        session = self.fake_services.stripe.get_session(response.json()['session_id'])

        assert 'setup_intent_data' in session
        assert 'metadata' in session['setup_intent_data']
        assert session['setup_intent_data']['metadata']['session_purpose'] == 'card_update'

        assert 'subscription_id' in session['setup_intent_data']['metadata']

class ReferralAPITests(SubscribedSubscriberAppTestCase):
    def test_get_referral_data(self):
        response = self.get('/data/referral/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('referral_code', response.json())
        self.assertIn('referral_credit', response.json())
        self.assertIn('referrals', response.json())

    def test_record_new_referral_increases_credit(self):
        referrer = SubscriberFactory()
        new_subscriber = SubscriberFactory()
        initial_credit = Decimal(referrer.referral_credit)
        credit_amount = Decimal('10.00')

        referrer.record_new_referral(new_subscriber, credit_amount)

        referrer.refresh_from_db()
        self.assertEqual(referrer.referral_credit, initial_credit + credit_amount)
        self.assertEqual(Referral.objects.count(), 1)
        referral = Referral.objects.first()
        self.assertEqual(referral.referrer, referrer)
        self.assertEqual(referral.referred_subscriber, new_subscriber)
        self.assertEqual(referral.referral_code, referrer.referral_code)
        self.assertEqual(referral.credit_applied, credit_amount)

    def test_get_referral_data_verify(self):
        referrer = SubscriberFactory()
        new_subscriber = SubscriberFactory()
        credit_amount = Decimal('10.00')

        referrer.record_new_referral(new_subscriber, credit_amount)
        referrer.refresh_from_db()
        new_subscriber.refresh_from_db()
        self.subscriber = referrer
        self.client = referrer.client

        response = self.get('/data/referral/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('referral_code', response.json())
        self.assertIn('referral_credit', response.json())
        self.assertIn('referrals', response.json())
        self.assertEqual(len(response.json()['referrals']), 1)
        self.assertEqual(response.json()['referrals'][0]['name'], new_subscriber.name)
        self.assertEqual(response.json()['referrals'][0]['credit_applied'], str(credit_amount))

class PerkRedemptionTests(SubscribedSubscriberAppTestCase):
    def setUp(self):
        super().setUp()
        self.simpnexus_client = ClientFactory(name='simpnexus')
        self.test_perk = VoucherPerk.objects.create(
            client=self.client,
            name='Test Perk',
            description='Test description',
            eligibility_threshold=10,
            enabled=True,
            electively_redeemable=False,
            eligibility_type=VoucherPerk.EligibilityType.NO_FREE,
        )
        self.subscriber.perk_points = 100
        self.subscriber.save()
        self.simpnexus_test_perk = VoucherPerk.objects.create(
            client=self.simpnexus_client,
            name='Test Perk',
            description='Test description',
            eligibility_threshold=10,
            enabled=True,
            electively_redeemable=False,
            eligibility_type=VoucherPerk.EligibilityType.NO_FREE,
        )
        self.simpnexus_subscriber = SubscriberFactory(
            client=self.simpnexus_client,
            perk_points=100
        )
        self.simpnexus_subscription = SubscriptionFactory(
            using=True,
            subscriber=self.simpnexus_subscriber
        )

    def test_auto_confirm_for_regular_client(self):
        response = self.post('/data/claim-perk/', {
            'id': self.test_perk.id,
            'claim_with_points': True
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        redemption = self.subscriber.perk_redemptions.first()
        self.assertTrue(redemption.is_confirmed)
        self.assertNotEqual(redemption.fulfilment_status, 'awaiting_confirmation')

    def test_simpnexus_requires_confirmation(self):
        self.subscriber = self.simpnexus_subscriber
        self.client = self.simpnexus_client
        response = self.post('/data/claim-perk/', {
            'id': self.simpnexus_test_perk.id,
            'claim_with_points': True
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        redemption = self.simpnexus_subscriber.perk_redemptions.first()
        self.assertFalse(redemption.is_confirmed)
        self.assertEqual(redemption.fulfilment_status, 'awaiting_confirmation')

    def test_confirm_endpoint(self):
        self.subscriber = self.simpnexus_subscriber
        self.client = self.simpnexus_client
        response = self.post('/data/claim-perk/', {
            'id': self.simpnexus_test_perk.id,
            'claim_with_points': True
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        redemption = self.simpnexus_subscriber.perk_redemptions.first()
        response = self.post(f'/data/perks/{redemption.id}/confirm/', {})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        redemption.refresh_from_db()
        self.assertTrue(redemption.is_confirmed)
        self.assertNotEqual(redemption.fulfilment_status, 'awaiting_confirmation')

    def test_cannot_confirm_already_confirmed(self):
        self.subscriber = self.simpnexus_subscriber
        self.client = self.simpnexus_client
        response = self.post('/data/claim-perk/', {
            'id': self.simpnexus_test_perk.id,
            'claim_with_points': True
        })
        redemption = self.simpnexus_subscriber.perk_redemptions.first()
        redemption.is_confirmed = True
        redemption.save()
        response = self.post(f'/data/perks/{redemption.id}/confirm/', {})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

class DeliverooTestCase(NexusTestCase):
    def setUp(self):
        super().setUp()
        self.api_client = APIClient()
        self.client = ClientFactory()
        self.subscriber = SubscriberFactory(client=self.client)

    def test_subscriber_metadata_field_exists(self):
        self.assertTrue(hasattr(Subscriber, 'metadata'), "Subscriber model should have metadata field")

        subscriber = Subscriber(
            email='<EMAIL>',
            client=self.client,
            name='Test User',
            metadata={'rider_id': 'RID123456', 'partner': 'deliveroo'}
        )

        self.assertEqual(subscriber.metadata['rider_id'], 'RID123456')

    def test_nexus_interface_sign_up_user_signature(self):
        sig = inspect.signature(NexusInterface.sign_up_user)

        self.assertIn('metadata', sig.parameters, "sign_up_user should accept metadata parameter")

    def test_validate_rider_missing_parameters(self):
        response = self.api_client.post(
            f'/s/api/v1/{self.client.id}/deliveroo/validate-rider/',
            {'email': '<EMAIL>'},
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Both email and rider_id are required', response.data['error'])
        response = self.api_client.post(
            f'/s/api/v1/{self.client.id}/deliveroo/validate-rider/',
            {'rider_id': 'RID123456'},
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Both email and rider_id are required', response.data['error'])

    def test_validate_rider_not_found(self):
        response = self.api_client.post(
            f'/s/api/v1/{self.client.id}/deliveroo/validate-rider/',
            {
                'email': '<EMAIL>',
                'rider_id': 'NONEXISTENT123'
            },
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['valid'], False)

    def test_upload_rider_data_invalid_format(self):
        response = self.api_client.post(
            f'/s/api/v1/{self.client.id}/deliveroo/upload-rider-data/',
            {},
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('lookup_keys must be a non-empty list', response.data['error'])
        response = self.api_client.post(
            f'/s/api/v1/{self.client.id}/deliveroo/upload-rider-data/',
            {'lookup_keys': []},
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('lookup_keys must be a non-empty list', response.data['error'])
        response = self.api_client.post(
            f'/s/api/v1/{self.client.id}/deliveroo/upload-rider-data/',
            {'lookup_keys': 'not_a_list'},
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('lookup_keys must be a non-empty list', response.data['error'])

    def test_upload_and_validate_rider_workflow(self):
        test_email = '<EMAIL>'
        test_rider_id = 'RID123456'
        lookup_data = f"{test_email.lower()}:{test_rider_id}"
        lookup_key = hashlib.sha256(lookup_data.encode('utf-8')).hexdigest()

        upload_response = self.api_client.post(
            f'/s/api/v1/{self.client.id}/deliveroo/upload-rider-data/',
            {'lookup_keys': [lookup_key, 'another_hash_key']},
            format='json'
        )
        self.assertEqual(upload_response.status_code, status.HTTP_200_OK)
        self.assertEqual(upload_response.data['uploaded_count'], 2)
        self.assertEqual(upload_response.data['total_provided'], 2)

        self.assertEqual(DeliverooRiderData.objects.filter(client=self.client).count(), 2)
        self.assertTrue(DeliverooRiderData.objects.filter(
            client=self.client,
            lookup_key=lookup_key
        ).exists())

        validate_response = self.api_client.post(
            f'/s/api/v1/{self.client.id}/deliveroo/validate-rider/',
            {
                'email': test_email,
                'rider_id': test_rider_id
            },
            format='json'
        )
        self.assertEqual(validate_response.status_code, status.HTTP_200_OK)
        self.assertEqual(validate_response.data['valid'], True)

        validate_response = self.api_client.post(
            f'/s/api/v1/{self.client.id}/deliveroo/validate-rider/',
            {
                'email': '<EMAIL>',
                'rider_id': 'NONEXISTENT123'
            },
            format='json'
        )
        self.assertEqual(validate_response.status_code, status.HTTP_200_OK)
        self.assertEqual(validate_response.data['valid'], False)

    def test_upload_rider_data_replaces_existing(self):
        initial_keys = ['hash1', 'hash2', 'hash3']
        response = self.api_client.post(
            f'/s/api/v1/{self.client.id}/deliveroo/upload-rider-data/',
            {'lookup_keys': initial_keys},
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(DeliverooRiderData.objects.filter(client=self.client).count(), 3)

        new_keys = ['hash4', 'hash5']
        response = self.api_client.post(
            f'/s/api/v1/{self.client.id}/deliveroo/upload-rider-data/',
            {'lookup_keys': new_keys},
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(DeliverooRiderData.objects.filter(client=self.client).count(), 2)

        self.assertFalse(DeliverooRiderData.objects.filter(
            client=self.client,
            lookup_key='hash1'
        ).exists())
        self.assertTrue(DeliverooRiderData.objects.filter(
            client=self.client,
            lookup_key='hash4'
        ).exists())

    def test_hashing_consistency(self):
        email = '<EMAIL>'
        rider_id = 'RID123456'

        lookup_data = f"{email.lower()}:{rider_id}"
        hash1 = hashlib.sha256(lookup_data.encode('utf-8')).hexdigest()
        hash2 = hashlib.sha256(lookup_data.encode('utf-8')).hexdigest()

        self.assertEqual(hash1, hash2)
        lookup_data_upper = f"{email.upper()}:{rider_id}"
        hash_upper = hashlib.sha256(lookup_data_upper.encode('utf-8')).hexdigest()
        self.assertNotEqual(hash1, hash_upper)

    def test_client_isolation(self):
        other_client = ClientFactory()
        response = self.api_client.post(
            f'/s/api/v1/{self.client.id}/deliveroo/upload-rider-data/',
            {'lookup_keys': ['hash1', 'hash2']},
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response = self.api_client.post(
            f'/s/api/v1/{other_client.id}/deliveroo/upload-rider-data/',
            {'lookup_keys': ['hash3', 'hash4']},
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(DeliverooRiderData.objects.filter(client=self.client).count(), 2)
        self.assertEqual(DeliverooRiderData.objects.filter(client=other_client).count(), 2)
        self.assertTrue(DeliverooRiderData.objects.filter(
            client=self.client,
            lookup_key='hash1'
        ).exists())
        self.assertFalse(DeliverooRiderData.objects.filter(
            client=other_client,
            lookup_key='hash1'
        ).exists())

class BasketCheckoutTests(SubscriberAppTestCase):
    stripe_backend = 'fake'

    def setUp(self):
        super().setUp()
        self.scenario.setup_billing_for_client(self.client)
        self.main_plan = PlanFactory(client=self.client, billing_plan_id='price_main_plan')
        self.child_plan = PlanFactory(client=self.client, billing_plan_id='price_child_plan')

    def test_valid_basket_checkout(self):
        basket = [{"id": str(self.main_plan.id)}]

        response = self.post('/basket/checkout/', {'basket': basket})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()

        self.assertIn('client_secret', response_data)
        self.assertIn('session_id', response_data)
        session = self.fake_services.stripe.get_session(response_data['session_id'])
        self.assertIn('billing_cycle_anchor', session['subscription_data'])
        self.assertEqual(session['subscription_data']['metadata']['plan_id'], str(self.main_plan.id))

    def test_empty_basket_validation(self):
        response = self.post('/basket/checkout/', {'basket': []})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.json())
        self.assertIn('empty', response.json()['error'].lower())

    def test_missing_basket_validation(self):
        response = self.post('/basket/checkout/', {})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.json())
        self.assertIn('required', response.json()['error'].lower())

    def test_invalid_basket_structure(self):
        response = self.post('/basket/checkout/', {'basket': 'invalid'})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        response = self.post('/basket/checkout/', {'basket': [{'addons': [1]}]})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        response = self.post('/basket/checkout/', {'basket': [{'id': 'invalid_id'}]})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_nonexistent_plan_validation(self):
        basket = [{"id": "99999"}]
        response = self.post('/basket/checkout/', {'basket': basket})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.json())
        self.assertIn('not found', response.json()['error'].lower())

    def test_basket_with_multiple_plans(self):
        basket = [
            {"id": str(self.main_plan.id)},
            {"id": str(self.child_plan.id)}
        ]
        response = self.post('/basket/checkout/', {'basket': basket})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()

        session = self.fake_services.stripe.get_session(response_data['session_id'])

        self.assertIn('billing_cycle_anchor', session['subscription_data'])

        self.assertEqual(session['subscription_data']['metadata']['plan_id'], str(self.main_plan.id))

    def test_comprehensive_basket_with_deduplication(self):  # pylint: disable=too-many-locals
        travel_addon_1gb = TravelAddon.objects.create(
            name="Europe 1GB",
            region="Europe",
            price=1.00,
            is_active=True
        )
        travel_addon_2gb = TravelAddon.objects.create(
            name="Europe 2GB",
            region="Europe",
            price=2.00,
            is_active=True
        )
        PlanTravelAddon.objects.create(
            plan=self.main_plan,
            travel_addon=travel_addon_1gb,
            is_active=True
        )
        PlanTravelAddon.objects.create(
            plan=self.main_plan,
            travel_addon=travel_addon_2gb,
            is_active=True
        )
        PlanTravelAddon.objects.create(
            plan=self.child_plan,
            travel_addon=travel_addon_1gb,
            is_active=True
        )
        FamilyPlanLink.objects.create(
            main_plan=self.main_plan,
            sub_plan=self.child_plan,
            price_override=8.00,
            is_active=True
        )
        basket = [
            {
                "id": str(self.main_plan.id),
                "addons": [travel_addon_1gb.id]
            },
            {
                "id": str(self.main_plan.id),
                "addons": [travel_addon_2gb.id]
            },
            {
                "id": str(self.main_plan.id),
                "child_plans": [
                    {
                        "id": str(self.child_plan.id),
                        "addons": [travel_addon_1gb.id]
                    }
                ]
            },
            {
                "id": str(self.child_plan.id)
            }
        ]

        response = self.post('/basket/checkout/', {'basket': basket})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertIn('client_secret', response_data)
        self.assertIn('session_id', response_data)
        self.assertIn('amount_total', response_data)
        view = BasketCheckoutView()
        services = view._initialize_services(self.client.obfuscated_id, self.subscriber.cognito_username)  # pylint: disable=protected-access
        line_items = view._build_stripe_line_items(basket, services['nexus_interface'])  # pylint: disable=protected-access
        billing_plan_counts = {}
        addon_items = []

        for item in line_items:
            if 'price' in item:
                billing_plan_id = item['price']
                billing_plan_counts[billing_plan_id] = item['quantity']
            else:
                addon_items.append(item)

        self.assertEqual(billing_plan_counts.get('price_main_plan'), 3)
        self.assertEqual(billing_plan_counts.get('price_child_plan'), 2)
        self.assertEqual(len(addon_items), 3)
        addon_names = [item['price_data']['product_data']['name'] for item in addon_items]
        self.assertIn('Travel Addon: Europe 1GB', addon_names)
        self.assertIn('Travel Addon: Europe 2GB', addon_names)
        self.assertIn('Travel Addon: Europe 1GB (Child Plan)', addon_names)
        for addon_item in addon_items:
            self.assertEqual(addon_item['quantity'], 1)

class PurchaseIntentDetailViewTest(SubscriberAppTestCase):
    def setUp(self):
        super().setUp()

        self.purchase_intent = PurchaseIntent.objects.create(
            purchase_type=PurchaseIntent.PurchaseType.PLAN_SIGNUP,
            status=PurchaseIntent.Status.PAYMENT_COMPLETED,
            subscriber=self.subscriber,
            client=self.client,
            stripe_session_id='cs_test_session_123',
            purchase_data={
                'original_basket': {
                    'plan_id': '1',
                    'plan_title': 'Test Plan',
                    'price': '16.00',
                    'billing_plan_id': 'plan_123',
                    'correlation_id': 'test123',
                    'ui_mode': 'embedded'
                },
                'stripe_line_items': [
                    {
                        'price': 'plan_123',
                        'quantity': 1
                    }
                ]
            },
            is_dry_run=False
        )

    def test_successful_retrieval(self):
        with MonkeyPatchSubscriberAuth(self.subscriber):
            response = self.get(f'/purchase-intent/details/?session_id={self.purchase_intent.stripe_session_id}')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertEqual(data['purchase_intent_id'], str(self.purchase_intent.id))
        self.assertEqual(data['purchase_type'], 'plan_signup')
        self.assertEqual(data['status'], 'payment_completed')
        self.assertEqual(data['stripe_session_id'], 'cs_test_session_123')

        self.assertIn('order_summary', data)
        order_summary = data['order_summary']
        self.assertEqual(order_summary['total_cost'], '16.00')
        self.assertEqual(order_summary['currency'], 'GBP')
        self.assertEqual(len(order_summary['items']), 1)

        item = order_summary['items'][0]
        self.assertEqual(item['type'], 'plan')
        self.assertEqual(item['name'], 'Test Plan')
        self.assertEqual(item['price'], '16.00')
        self.assertEqual(item['quantity'], 1)

        self.assertIn('original_basket', data)
        original_basket = data['original_basket']
        self.assertEqual(original_basket['plan_id'], '1')
        self.assertEqual(original_basket['plan_title'], 'Test Plan')
        self.assertEqual(original_basket['price'], '16.00')
        self.assertEqual(original_basket['billing_plan_id'], 'plan_123')

        self.assertIn('stripe_line_items', data)
        stripe_line_items = data['stripe_line_items']
        self.assertEqual(len(stripe_line_items), 1)
        self.assertEqual(stripe_line_items[0]['price'], 'plan_123')
        self.assertEqual(stripe_line_items[0]['quantity'], 1)

    def test_missing_session_id(self):
        with MonkeyPatchSubscriberAuth(self.subscriber):
            response = self.get('/purchase-intent/details/')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        data = response.json()
        self.assertIn('error', data)
        self.assertEqual(data['error'], 'session_id parameter is required')

    def test_purchase_intent_not_found(self):
        with MonkeyPatchSubscriberAuth(self.subscriber):
            response = self.get('/purchase-intent/details/?session_id=cs_nonexistent_session')

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        data = response.json()
        self.assertIn('error', data)
        self.assertEqual(data['error'], 'Purchase intent not found for the provided session_id')

    def test_unauthorized_access(self):
        other_client = ClientFactory()
        other_subscriber = SubscriberFactory(
            client=other_client,
            cognito_username='<EMAIL>'
        )

        other_purchase_intent = PurchaseIntent.objects.create(
            purchase_type=PurchaseIntent.PurchaseType.PLAN_SIGNUP,
            status=PurchaseIntent.Status.PAYMENT_COMPLETED,
            subscriber=other_subscriber,
            client=other_client,
            stripe_session_id='cs_test_other_session_456',
            purchase_data={
                'original_basket': {
                    'plan_id': '2',
                    'plan_title': 'Other Plan',
                    'price': '20.00',
                    'billing_plan_id': 'plan_456'
                },
                'stripe_line_items': [
                    {
                        'price': 'plan_456',
                        'quantity': 1
                    }
                ]
            },
            is_dry_run=False
        )

        with MonkeyPatchSubscriberAuth(self.subscriber):
            response = self.get(f'/purchase-intent/details/?session_id={other_purchase_intent.stripe_session_id}')

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        data = response.json()
        self.assertIn('error', data)
        self.assertEqual(data['error'], 'Unauthorized access to purchase intent')

    def test_basket_checkout_order_summary(self):  # pylint: disable=too-many-locals,too-many-statements
        main_plan = PlanFactory(client=self.client, name="Main Plan", price=Decimal('10.00'))
        child_plan = PlanFactory(client=self.client, name="Child Plan", price=Decimal('8.00'))

        travel_addon_1gb = TravelAddon.objects.create(
            name="1GB Travel Addon",
            region="Europe",
            price=Decimal('4.00'),
            is_active=True
        )
        travel_addon_2gb = TravelAddon.objects.create(
            name="2GB Travel Addon",
            region="Europe",
            price=Decimal('6.00'),
            is_active=True
        )
        PlanTravelAddon.objects.create(
            plan=main_plan,
            travel_addon=travel_addon_1gb,
            price_override=Decimal('3.00'),
            is_active=True
        )
        PlanTravelAddon.objects.create(
            plan=main_plan,
            travel_addon=travel_addon_2gb,
            price_override=Decimal('5.00'),
            is_active=True
        )
        PlanTravelAddon.objects.create(
            plan=child_plan,
            travel_addon=travel_addon_1gb,
            price_override=Decimal('2.00'),
            is_active=True
        )
        basket = [
            {
                "id": str(main_plan.id),
                "addons": [travel_addon_1gb.id]
            },
            {
                "id": str(main_plan.id),
                "addons": [travel_addon_2gb.id]
            },
            {
                "id": str(main_plan.id),
                "child_plans": [
                    {
                        "id": str(child_plan.id),
                        "addons": [travel_addon_1gb.id]
                    }
                ]
            },
            {
                "id": str(child_plan.id)
            }
        ]

        basket_purchase_intent = PurchaseIntent.objects.create(
            purchase_type=PurchaseIntent.PurchaseType.BASKET_CHECKOUT,
            status=PurchaseIntent.Status.PAYMENT_COMPLETED,
            subscriber=self.subscriber,
            client=self.client,
            stripe_session_id='cs_test_basket_session_789',
            purchase_data={
                'basket': basket,
                'correlation_id': 'test_correlation_123',
                'ui_mode': 'embedded',
                'return_path': '/checkout/return',
                'total_plans': 4,
                'total_addons': 3,
                'total_child_plans': 1
            },
            is_dry_run=False
        )

        with MonkeyPatchSubscriberAuth(self.subscriber):
            response = self.get(f'/purchase-intent/details/?session_id={basket_purchase_intent.stripe_session_id}')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data['purchase_intent_id'], str(basket_purchase_intent.id))
        self.assertEqual(data['purchase_type'], 'basket_checkout')
        self.assertEqual(data['status'], 'payment_completed')
        order_summary = data['order_summary']
        self.assertIn('items', order_summary)
        self.assertIn('total_cost', order_summary)
        self.assertIn('currency', order_summary)
        self.assertEqual(order_summary['currency'], 'GBP')

        items = order_summary['items']
        self.assertEqual(len(items), 4)

        item1 = items[0]
        self.assertEqual(item1['id'], main_plan.id)
        self.assertEqual(item1['type'], 'plan')
        self.assertEqual(item1['name'], 'Main Plan')
        self.assertEqual(item1['price'], '10.00')
        self.assertEqual(item1['quantity'], 1)
        self.assertIn('addons', item1)
        self.assertEqual(len(item1['addons']), 1)
        self.assertEqual(item1['addons'][0]['name'], '1GB Travel Addon')
        self.assertEqual(item1['addons'][0]['price'], '3.00')

        item2 = items[1]
        self.assertEqual(item2['id'], main_plan.id)
        self.assertEqual(item2['type'], 'plan')
        self.assertEqual(item2['name'], 'Main Plan')
        self.assertIn('addons', item2)
        self.assertEqual(len(item2['addons']), 1)
        self.assertEqual(item2['addons'][0]['name'], '2GB Travel Addon')
        self.assertEqual(item2['addons'][0]['price'], '5.00')

        item3 = items[2]
        self.assertEqual(item3['id'], main_plan.id)
        self.assertIn('child_plans', item3)
        self.assertEqual(len(item3['child_plans']), 1)
        child_plan_item = item3['child_plans'][0]
        self.assertEqual(child_plan_item['id'], child_plan.id)
        self.assertEqual(child_plan_item['name'], 'Child Plan')
        self.assertEqual(child_plan_item['price'], '8.00')
        self.assertIn('addons', child_plan_item)
        self.assertEqual(child_plan_item['addons'][0]['name'], '1GB Travel Addon')
        self.assertEqual(child_plan_item['addons'][0]['price'], '2.00')

        item4 = items[3]
        self.assertEqual(item4['id'], child_plan.id)
        self.assertEqual(item4['type'], 'plan')
        self.assertEqual(item4['name'], 'Child Plan')
        self.assertEqual(item4['price'], '8.00')

        expected_total = Decimal('56.00')
        self.assertEqual(order_summary['total_cost'], f"{expected_total:.2f}")
